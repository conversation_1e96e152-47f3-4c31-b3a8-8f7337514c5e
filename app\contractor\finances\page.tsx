"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Clock, 
  CheckCircle,
  AlertCircle,
  Download,
  Plus,
  Eye,
  Send,
  CreditCard,
  Wallet,
  FileText,
  Calendar,
  Filter,
  Search,
  ArrowUpRight,
  ArrowDownLeft,
  Banknote,
  Receipt
} from "lucide-react";

interface Transaction {
  id: string;
  type: 'payment' | 'withdrawal' | 'fee' | 'refund';
  amount: number;
  description: string;
  date: string;
  status: 'completed' | 'pending' | 'failed';
  projectId?: string;
  projectTitle?: string;
}

interface Invoice {
  id: string;
  projectTitle: string;
  clientName: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  dueDate: string;
  sentDate?: string;
  paidDate?: string;
}

const mockTransactions: Transaction[] = [
  {
    id: "1",
    type: "payment",
    amount: 2500,
    description: "Kitchen renovation project payment",
    date: "2024-01-15",
    status: "completed",
    projectId: "proj_1",
    projectTitle: "Kitchen Renovation - Johnson"
  },
  {
    id: "2", 
    type: "withdrawal",
    amount: -1500,
    description: "Bank transfer to Wells Fargo ****1234",
    date: "2024-01-14",
    status: "completed"
  },
  {
    id: "3",
    type: "fee",
    amount: -75,
    description: "Platform service fee (3%)",
    date: "2024-01-15",
    status: "completed"
  }
];

const mockInvoices: Invoice[] = [
  {
    id: "inv_1",
    projectTitle: "Bathroom Renovation",
    clientName: "Michael Chen",
    amount: 8500,
    status: "sent",
    dueDate: "2024-01-25",
    sentDate: "2024-01-10"
  },
  {
    id: "inv_2",
    projectTitle: "Kitchen Renovation",
    clientName: "Sarah Johnson", 
    amount: 18000,
    status: "paid",
    dueDate: "2024-01-20",
    sentDate: "2024-01-05",
    paidDate: "2024-01-15"
  }
];

export default function ContractorFinancesPage() {
  const router = useRouter();
  const [transactions] = useState<Transaction[]>(mockTransactions);
  const [invoices] = useState<Invoice[]>(mockInvoices);
  const [withdrawalAmount, setWithdrawalAmount] = useState("");

  const totalBalance = 4250;
  const pendingPayments = 8500;
  const monthlyEarnings = 12750;
  const availableForWithdrawal = 2750;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'paid':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'pending':
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'draft':
        return 'bg-slate-100 text-slate-800 border-slate-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <ArrowDownLeft className="h-4 w-4 text-emerald-600" />;
      case 'withdrawal':
        return <ArrowUpRight className="h-4 w-4 text-blue-600" />;
      case 'fee':
        return <Receipt className="h-4 w-4 text-orange-600" />;
      case 'refund':
        return <ArrowDownLeft className="h-4 w-4 text-purple-600" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <Wallet className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Financial Dashboard</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Manage your earnings and payments</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline" onClick={() => router.push('/contractor/finances/invoices/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Invoice
              </Button>
              <Button onClick={() => router.push('/contractor/finances/reports')}>
                <Download className="h-4 w-4 mr-2" />
                Download Report
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Financial Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Total Balance</p>
                  <p className="text-3xl font-bold">${totalBalance.toLocaleString()}</p>
                  <p className="text-emerald-200 text-sm">Available now</p>
                </div>
                <Wallet className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Pending Payments</p>
                  <p className="text-3xl font-bold">${pendingPayments.toLocaleString()}</p>
                  <p className="text-blue-200 text-sm">In escrow</p>
                </div>
                <Clock className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">This Month</p>
                  <p className="text-3xl font-bold">${monthlyEarnings.toLocaleString()}</p>
                  <div className="flex items-center gap-1 text-orange-200 text-sm">
                    <TrendingUp className="h-3 w-3" />
                    +12% vs last month
                  </div>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Available to Withdraw</p>
                  <p className="text-3xl font-bold">${availableForWithdrawal.toLocaleString()}</p>
                  <p className="text-purple-200 text-sm">Ready for transfer</p>
                </div>
                <Banknote className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="transactions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="withdraw">Withdraw Funds</TabsTrigger>
          </TabsList>

          {/* Transactions Tab */}
          <TabsContent value="transactions" className="space-y-6">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Transactions</CardTitle>
                    <CardDescription>Your payment history and account activity</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-white dark:bg-slate-600 rounded-lg">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-white">
                            {transaction.description}
                          </p>
                          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
                            <Calendar className="h-3 w-3" />
                            {new Date(transaction.date).toLocaleDateString()}
                            {transaction.projectTitle && (
                              <>
                                <span>•</span>
                                <span>{transaction.projectTitle}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${
                          transaction.amount > 0 ? 'text-emerald-600' : 'text-red-600'
                        }`}>
                          {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount).toLocaleString()}
                        </p>
                        <Badge className={`text-xs ${getStatusColor(transaction.status)}`}>
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Invoices Tab */}
          <TabsContent value="invoices" className="space-y-6">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Invoice Management</CardTitle>
                    <CardDescription>Create and track your project invoices</CardDescription>
                  </div>
                  <Button onClick={() => router.push('/contractor/finances/invoices/create')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-white dark:bg-slate-600 rounded-lg">
                          <FileText className="h-4 w-4 text-slate-600" />
                        </div>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-white">
                            {invoice.projectTitle}
                          </p>
                          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
                            <span>Client: {invoice.clientName}</span>
                            <span>•</span>
                            <span>Due: {new Date(invoice.dueDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-semibold text-slate-900 dark:text-white">
                            ${invoice.amount.toLocaleString()}
                          </p>
                          <Badge className={`text-xs ${getStatusColor(invoice.status)}`}>
                            {invoice.status}
                          </Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {invoice.status === 'draft' && (
                            <Button size="sm">
                              <Send className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Withdraw Tab */}
          <TabsContent value="withdraw" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Withdraw Funds</CardTitle>
                  <CardDescription>
                    Transfer your earnings to your bank account
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Wallet className="h-5 w-5 text-emerald-600" />
                      <div>
                        <p className="font-medium text-emerald-800 dark:text-emerald-200">
                          Available Balance
                        </p>
                        <p className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">
                          ${availableForWithdrawal.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Withdrawal Amount</label>
                      <Input
                        type="number"
                        placeholder="Enter amount"
                        value={withdrawalAmount}
                        onChange={(e) => setWithdrawalAmount(e.target.value)}
                        className="text-lg"
                      />
                    </div>

                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CreditCard className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-800 dark:text-blue-200">
                          Wells Fargo ****1234
                        </span>
                      </div>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Funds typically arrive in 1-2 business days
                      </p>
                    </div>

                    <Button 
                      className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                      disabled={!withdrawalAmount || parseFloat(withdrawalAmount) > availableForWithdrawal}
                    >
                      <Banknote className="h-4 w-4 mr-2" />
                      Request Withdrawal
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Recent Withdrawals</CardTitle>
                  <CardDescription>Your withdrawal history</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div>
                        <p className="font-medium">$1,500.00</p>
                        <p className="text-sm text-slate-600 dark:text-slate-300">Jan 14, 2024</p>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">
                        Completed
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div>
                        <p className="font-medium">$2,200.00</p>
                        <p className="text-sm text-slate-600 dark:text-slate-300">Jan 7, 2024</p>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">
                        Completed
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
