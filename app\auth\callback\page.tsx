"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";
import { Loader2 } from "lucide-react";

export default function AuthCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error("Auth callback error:", error);
          router.push("/?error=auth_error");
          return;
        }

        if (data.session) {
          console.log(
            "Session found, user email confirmed:",
            data.session.user.email_confirmed_at
          );

          // If this is an email verification callback, update the profile
          if (data.session.user.email_confirmed_at) {
            // Update the profile to mark email as verified
            const { error: updateError } = await supabase
              .from("profiles")
              .update({
                email_verified: true,
                updated_at: new Date().toISOString(),
              })
              .eq("id", data.session.user.id);

            if (updateError) {
              console.error("Error updating profile:", updateError);
            } else {
              console.log("Profile updated with email verification");
            }
          }

          // Add a small delay to ensure the profile update is processed
          setTimeout(() => {
            router.push("/");
          }, 500);
        } else {
          // No session, redirect to home
          router.push("/");
        }
      } catch (error) {
        console.error("Unexpected error:", error);
        router.push("/?error=unexpected_error");
      }
    };

    handleAuthCallback();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-orange-600" />
        <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
          Completing authentication...
        </h2>
        <p className="text-muted-foreground">
          Please wait while we verify your account.
        </p>
      </div>
    </div>
  );
}
