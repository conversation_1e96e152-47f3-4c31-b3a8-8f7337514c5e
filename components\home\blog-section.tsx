import Link from "next/link";
import { blogPosts } from "@/lib/data";
import { formatDate } from "@/lib/utils";

export function BlogSection() {
  return (
    <section className="section-padding bg-background">
      <div className="container">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12 lg:mb-16">
          <div className="max-w-2xl">
            <h2 className="heading-2 mb-4">Latest From Our Blog</h2>
            <p className="body-medium text-muted-foreground">
              Get interesting insights, articles, and news from our experts
            </p>
          </div>
          <Link
            href="/blog"
            className="text-orange-600 dark:text-orange-400 font-semibold hover:underline mt-6 md:mt-0 flex items-center gap-2 group"
          >
            View All Articles
            <svg
              className="w-4 h-4 group-hover:translate-x-1 transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
          {blogPosts.map((post) => (
            <Link key={post.id} href={`/blog/${post.slug}`}>
              <div className="group rounded-2xl border bg-white dark:bg-slate-800 overflow-hidden card-hover">
                <div className="relative h-48 w-full overflow-hidden">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="p-6">
                  <div className="text-sm font-semibold text-orange-600 dark:text-orange-400 mb-3 uppercase tracking-wide">
                    {post.category}
                  </div>
                  <h3 className="font-semibold text-lg mb-3 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-muted-foreground text-sm line-clamp-2 mb-4 leading-relaxed">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      By {post.author}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(post.date)}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
