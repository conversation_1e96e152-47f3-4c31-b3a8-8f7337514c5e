'use client'

import { UserR<PERSON> } from '@/lib/supabase/types'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  Star, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Shield,
  Clock,
  Award,
  Zap,
  Target,
  Sparkles,
  ArrowRight,
  Gift,
  Crown,
  Rocket,
  Home,
  Briefcase,
  Hammer
} from 'lucide-react'

interface CompletionIncentivesProps {
  role: UserRole
  currentPercentage: number
  onContinue: () => void
  onSkip?: () => void
  showSkipOption?: boolean
}

export function CompletionIncentives({
  role,
  currentPercentage,
  onContinue,
  onSkip,
  showSkipOption = false
}: CompletionIncentivesProps) {
  
  const getRoleIcon = () => {
    switch (role) {
      case 'homeowner':
        return <Home className="h-8 w-8" />
      case 'contractor':
        return <Briefcase className="h-8 w-8" />
      case 'day_laborer':
        return <Hammer className="h-8 w-8" />
      default:
        return <Star className="h-8 w-8" />
    }
  }

  const getRoleTitle = () => {
    switch (role) {
      case 'homeowner':
        return 'Homeowner'
      case 'contractor':
        return 'Contractor'
      case 'day_laborer':
        return 'Day Laborer'
      default:
        return 'Profile'
    }
  }

  const getRoleColor = () => {
    switch (role) {
      case 'homeowner':
        return 'from-blue-500 to-indigo-600'
      case 'contractor':
        return 'from-emerald-500 to-teal-600'
      case 'day_laborer':
        return 'from-orange-500 to-red-600'
      default:
        return 'from-slate-500 to-slate-600'
    }
  }

  const getBenefits = () => {
    switch (role) {
      case 'homeowner':
        return [
          {
            icon: <Target className="h-5 w-5" />,
            title: 'Post Unlimited Projects',
            description: 'Create as many project listings as you need'
          },
          {
            icon: <Users className="h-5 w-5" />,
            title: 'Access Verified Contractors',
            description: 'Connect with licensed and insured professionals'
          },
          {
            icon: <Shield className="h-5 w-5" />,
            title: 'Secure Payment Protection',
            description: 'Your payments are protected until work is complete'
          },
          {
            icon: <Star className="h-5 w-5" />,
            title: 'Rate & Review System',
            description: 'Help others by sharing your experience'
          }
        ]
      case 'contractor':
        return [
          {
            icon: <Rocket className="h-5 w-5" />,
            title: 'Grow Your Business',
            description: 'Access thousands of potential customers'
          },
          {
            icon: <DollarSign className="h-5 w-5" />,
            title: 'Competitive Bidding',
            description: 'Set your own prices and win more projects'
          },
          {
            icon: <Award className="h-5 w-5" />,
            title: 'Build Your Reputation',
            description: 'Showcase your work and earn 5-star reviews'
          },
          {
            icon: <Shield className="h-5 w-5" />,
            title: 'Secure Payments',
            description: 'Get paid quickly and securely for your work'
          }
        ]
      case 'day_laborer':
        return [
          {
            icon: <Clock className="h-5 w-5" />,
            title: 'Flexible Schedule',
            description: 'Work when you want, where you want'
          },
          {
            icon: <Zap className="h-5 w-5" />,
            title: 'Quick Daily Payments',
            description: 'Get paid the same day you complete work'
          },
          {
            icon: <Target className="h-5 w-5" />,
            title: 'Local Opportunities',
            description: 'Find work in your neighborhood'
          },
          {
            icon: <TrendingUp className="h-5 w-5" />,
            title: 'Skill-Based Matching',
            description: 'Get matched with jobs that fit your skills'
          }
        ]
      default:
        return []
    }
  }

  const getStats = () => {
    switch (role) {
      case 'homeowner':
        return [
          { label: 'Projects Posted', value: '15,000+', icon: <Target className="h-4 w-4" /> },
          { label: 'Satisfaction Rate', value: '4.8/5', icon: <Star className="h-4 w-4" /> },
          { label: 'Average Savings', value: '30%', icon: <DollarSign className="h-4 w-4" /> }
        ]
      case 'contractor':
        return [
          { label: 'Active Contractors', value: '8,500+', icon: <Users className="h-4 w-4" /> },
          { label: 'Avg. Annual Revenue', value: '$75k+', icon: <DollarSign className="h-4 w-4" /> },
          { label: 'Business Growth', value: '40%', icon: <TrendingUp className="h-4 w-4" /> }
        ]
      case 'day_laborer':
        return [
          { label: 'Active Laborers', value: '3,200+', icon: <Users className="h-4 w-4" /> },
          { label: 'Average Hourly Rate', value: '$25-45', icon: <DollarSign className="h-4 w-4" /> },
          { label: 'Flexibility', value: '100%', icon: <Clock className="h-4 w-4" /> }
        ]
      default:
        return []
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <div className="inline-flex items-center gap-3 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-800 dark:text-orange-200 px-6 py-3 rounded-full">
          <Sparkles className="h-5 w-5" />
          <span className="font-medium">Let's complete your {getRoleTitle()} profile</span>
        </div>
        
        <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br ${getRoleColor()} text-white shadow-lg`}>
          {getRoleIcon()}
        </div>
        
        <div>
          <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
            Unlock Your {getRoleTitle()} Potential
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            Complete your profile to access all features and start connecting with opportunities on BuildPro.
          </p>
        </div>

        {/* Progress */}
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Profile Progress</span>
            <span className="text-sm font-bold text-orange-600 dark:text-orange-400">{currentPercentage}%</span>
          </div>
          <Progress value={currentPercentage} className="h-3" />
        </div>
      </div>

      {/* Benefits Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {getBenefits().map((benefit, index) => (
          <Card key={index} className="border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-lg bg-gradient-to-br ${getRoleColor()} text-white shadow-lg`}>
                  {benefit.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    {benefit.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Stats Section */}
      <Card className="border-0 bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 shadow-xl">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-xl">
            <Crown className="h-6 w-6 text-yellow-500" />
            Join Thousands of Successful {getRoleTitle()}s
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {getStats().map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${getRoleColor()} text-white`}>
                    {stat.icon}
                  </div>
                </div>
                <div className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-slate-600 dark:text-slate-300">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <div className="text-center space-y-6">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 text-emerald-800 dark:text-emerald-200 px-4 py-2 rounded-full text-sm">
          <Gift className="h-4 w-4" />
          Complete your profile and start earning today!
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showSkipOption && onSkip && (
            <Button
              variant="outline"
              onClick={onSkip}
              className="min-w-[160px]"
            >
              Skip for Now
            </Button>
          )}
          <Button
            onClick={onContinue}
            className={`min-w-[200px] bg-gradient-to-r ${getRoleColor()} hover:shadow-lg transition-all duration-300`}
          >
            Complete My Profile
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        <p className="text-xs text-slate-500 dark:text-slate-400 max-w-md mx-auto">
          It only takes 2-3 minutes to complete your profile and unlock all features.
        </p>
      </div>
    </div>
  )
}
