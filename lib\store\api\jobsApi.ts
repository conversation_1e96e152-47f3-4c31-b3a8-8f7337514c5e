import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { supabase } from '@/lib/supabase/client'

// Types for job postings
export interface JobPosting {
  id: string
  user_id: string
  title: string
  description: string
  category: string
  budget_min?: number
  budget_max?: number
  budget_type: 'fixed' | 'hourly' | 'negotiable'
  deadline?: string
  location_address?: string
  location_city?: string
  location_state?: string
  location_zip?: string
  status: 'active' | 'in_progress' | 'completed' | 'cancelled'
  urgency: 'low' | 'normal' | 'high' | 'urgent'
  requirements?: string[]
  images?: string[]
  created_at: string
  updated_at: string
}

export interface CreateJobPostingData {
  title: string
  description: string
  category: string
  budget_min?: number
  budget_max?: number
  budget_type: 'fixed' | 'hourly' | 'negotiable'
  deadline?: string
  location_address?: string
  location_city?: string
  location_state?: string
  location_zip?: string
  urgency?: 'low' | 'normal' | 'high' | 'urgent'
  requirements?: string[]
  images?: string[]
}

export interface JobApplication {
  id: string
  job_id: string
  contractor_id: string
  quote_amount?: number
  estimated_duration?: string
  proposal_text: string
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn'
  created_at: string
  updated_at: string
  contractor_profile?: {
    business_name?: string
    years_experience?: number
    hourly_rate?: number
  }
}

export interface CreateJobApplicationData {
  job_id: string
  quote_amount?: number
  estimated_duration?: string
  proposal_text: string
}

export const jobsApi = createApi({
  reducerPath: 'jobsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: async (headers) => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.access_token) {
        headers.set('authorization', `Bearer ${session.access_token}`)
      }
      return headers
    },
  }),
  tagTypes: ['JobPosting', 'JobApplication'],
  endpoints: (builder) => ({
    // Job Posting Endpoints
    createJobPosting: builder.mutation<JobPosting, CreateJobPostingData>({
      queryFn: async (jobData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('job_postings')
            .insert({
              ...jobData,
              user_id: user.id,
            })
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['JobPosting'],
    }),

    getMyJobPostings: builder.query<JobPosting[], void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('job_postings')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })

          if (error) throw error
          return { data: data || [] }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['JobPosting'],
    }),

    getJobPosting: builder.query<JobPosting, string>({
      queryFn: async (jobId) => {
        try {
          const { data, error } = await supabase
            .from('job_postings')
            .select('*')
            .eq('id', jobId)
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: (result, error, id) => [{ type: 'JobPosting', id }],
    }),

    updateJobPosting: builder.mutation<JobPosting, { id: string; data: Partial<CreateJobPostingData> }>({
      queryFn: async ({ id, data: updateData }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('job_postings')
            .update({
              ...updateData,
              updated_at: new Date().toISOString(),
            })
            .eq('id', id)
            .eq('user_id', user.id) // Ensure user owns the job
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'JobPosting', id }],
    }),

    deleteJobPosting: builder.mutation<void, string>({
      queryFn: async (jobId) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { error } = await supabase
            .from('job_postings')
            .delete()
            .eq('id', jobId)
            .eq('user_id', user.id) // Ensure user owns the job

          if (error) throw error
          return { data: undefined }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['JobPosting'],
    }),

    // Browse jobs (for contractors)
    browseJobs: builder.query<JobPosting[], { category?: string; location?: string; urgency?: string }>({
      queryFn: async (filters = {}) => {
        try {
          let query = supabase
            .from('job_postings')
            .select('*')
            .eq('status', 'active')
            .order('created_at', { ascending: false })

          if (filters.category) {
            query = query.eq('category', filters.category)
          }
          if (filters.location) {
            query = query.ilike('location_city', `%${filters.location}%`)
          }
          if (filters.urgency) {
            query = query.eq('urgency', filters.urgency)
          }

          const { data, error } = await query

          if (error) throw error
          return { data: data || [] }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['JobPosting'],
    }),

    // Job Application Endpoints
    createJobApplication: builder.mutation<JobApplication, CreateJobApplicationData>({
      queryFn: async (applicationData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('job_applications')
            .insert({
              ...applicationData,
              contractor_id: user.id,
            })
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['JobApplication'],
    }),

    getJobApplications: builder.query<JobApplication[], string>({
      queryFn: async (jobId) => {
        try {
          const { data, error } = await supabase
            .from('job_applications')
            .select(`
              *,
              contractor_profile:contractor_profiles!contractor_id (
                business_name,
                years_experience,
                hourly_rate
              )
            `)
            .eq('job_id', jobId)
            .order('created_at', { ascending: false })

          if (error) throw error
          return { data: data || [] }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: (result, error, jobId) => [{ type: 'JobApplication', id: jobId }],
    }),

    updateApplicationStatus: builder.mutation<JobApplication, { id: string; status: 'accepted' | 'rejected' }>({
      queryFn: async ({ id, status }) => {
        try {
          const { data, error } = await supabase
            .from('job_applications')
            .update({ status, updated_at: new Date().toISOString() })
            .eq('id', id)
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['JobApplication'],
    }),
  }),
})

export const {
  useCreateJobPostingMutation,
  useGetMyJobPostingsQuery,
  useGetJobPostingQuery,
  useUpdateJobPostingMutation,
  useDeleteJobPostingMutation,
  useBrowseJobsQuery,
  useCreateJobApplicationMutation,
  useGetJobApplicationsQuery,
  useUpdateApplicationStatusMutation,
} = jobsApi
