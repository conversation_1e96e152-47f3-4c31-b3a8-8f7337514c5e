'use client'

import { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { RootState } from '@/lib/store'
import { setActiveRole } from '@/lib/store/slices/authSlice'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Home, Briefcase, Hammer, ChevronDown, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { UserRole } from '@/lib/supabase/types'

export function RoleSwitcher() {
  const dispatch = useDispatch()
  const { selectedRoles, activeRole, profileCompletionStatus } = useSelector((state: RootState) => state.auth)

  if (!selectedRoles || selectedRoles.length === 0) {
    return null
  }

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'homeowner':
        return <Home className="h-4 w-4" />
      case 'contractor':
        return <Briefcase className="h-4 w-4" />
      case 'day_laborer':
        return <Hammer className="h-4 w-4" />
      default:
        return null
    }
  }

  const getRoleTitle = (role: UserRole) => {
    switch (role) {
      case 'homeowner':
        return 'Homeowner'
      case 'contractor':
        return 'Contractor'
      case 'day_laborer':
        return 'Day Laborer'
      default:
        return role
    }
  }

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'homeowner':
        return 'text-blue-600 dark:text-blue-400'
      case 'contractor':
        return 'text-emerald-600 dark:text-emerald-400'
      case 'day_laborer':
        return 'text-orange-600 dark:text-orange-400'
      default:
        return 'text-slate-600 dark:text-slate-400'
    }
  }

  const getCompletionStatus = (role: UserRole) => {
    const status = profileCompletionStatus?.[role]
    if (!status) return { icon: <Clock className="h-3 w-3" />, label: 'Pending', color: 'text-slate-500' }
    
    if (status.completed) {
      return { icon: <CheckCircle className="h-3 w-3" />, label: 'Complete', color: 'text-emerald-600' }
    } else {
      return { icon: <AlertCircle className="h-3 w-3" />, label: `${status.percentage}%`, color: 'text-orange-600' }
    }
  }

  const handleRoleSwitch = (role: UserRole) => {
    dispatch(setActiveRole(role))
  }

  // If only one role, show simple indicator
  if (selectedRoles.length === 1) {
    const role = selectedRoles[0]
    const completionStatus = getCompletionStatus(role)
    
    return (
      <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-100 dark:bg-slate-800">
        <div className={getRoleColor(role)}>
          {getRoleIcon(role)}
        </div>
        <span className="text-sm font-medium">{getRoleTitle(role)}</span>
        <div className={`flex items-center gap-1 ${completionStatus.color}`}>
          {completionStatus.icon}
          <span className="text-xs">{completionStatus.label}</span>
        </div>
      </div>
    )
  }

  // Multiple roles - show dropdown switcher
  const currentRole = activeRole || selectedRoles[0]
  const currentCompletionStatus = getCompletionStatus(currentRole)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 min-w-[160px]">
          <div className={getRoleColor(currentRole)}>
            {getRoleIcon(currentRole)}
          </div>
          <span className="flex-1 text-left">{getRoleTitle(currentRole)}</span>
          <div className={`flex items-center gap-1 ${currentCompletionStatus.color}`}>
            {currentCompletionStatus.icon}
            <span className="text-xs">{currentCompletionStatus.label}</span>
          </div>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-64">
        <DropdownMenuLabel>Switch Role</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {selectedRoles.map((role) => {
          const isActive = role === currentRole
          const completionStatus = getCompletionStatus(role)
          
          return (
            <DropdownMenuItem
              key={role}
              onClick={() => handleRoleSwitch(role)}
              className={`flex items-center gap-3 p-3 cursor-pointer ${
                isActive ? 'bg-orange-50 dark:bg-orange-950/20' : ''
              }`}
            >
              <div className={getRoleColor(role)}>
                {getRoleIcon(role)}
              </div>
              <div className="flex-1">
                <div className="font-medium">{getRoleTitle(role)}</div>
                <div className={`flex items-center gap-1 text-xs ${completionStatus.color}`}>
                  {completionStatus.icon}
                  <span>{completionStatus.label}</span>
                </div>
              </div>
              {isActive && (
                <Badge variant="secondary" className="text-xs">
                  Active
                </Badge>
              )}
            </DropdownMenuItem>
          )
        })}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-muted-foreground">
          <div className="text-xs">
            Switch between your roles to access different features and dashboards
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
