'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { useUpdateDayLaborerProfileMutation } from '@/lib/store/api/profileApi'
import { useDispatch } from 'react-redux'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Wrench, DollarSign, Car, Tool, Loader2 } from 'lucide-react'

const dayLaborerProfileSchema = z.object({
  skills: z.array(z.string()).min(1, 'Please select at least one skill'),
  hourly_rate: z.number().min(10, 'Hourly rate must be at least $10').max(200, 'Please enter a valid rate'),
  transportation: z.boolean().default(false),
  tools_owned: z.array(z.string()).optional(),
})

type DayLaborerProfileFormData = z.infer<typeof dayLaborerProfileSchema>

interface DayLaborerProfileFormProps {
  onComplete?: () => void
  initialData?: Partial<DayLaborerProfileFormData>
}

export function DayLaborerProfileForm({ onComplete, initialData }: DayLaborerProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedSkills, setSelectedSkills] = useState<string[]>(
    initialData?.skills || []
  )
  const [selectedTools, setSelectedTools] = useState<string[]>(
    initialData?.tools_owned || []
  )
  const dispatch = useDispatch()
  const [updateDayLaborerProfile] = useUpdateDayLaborerProfileMutation()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<DayLaborerProfileFormData>({
    resolver: zodResolver(dayLaborerProfileSchema),
    defaultValues: {
      ...initialData,
      skills: selectedSkills,
      tools_owned: selectedTools,
    },
  })

  const skills = [
    'General Labor',
    'Construction Helper',
    'Painting',
    'Cleaning',
    'Moving & Hauling',
    'Landscaping',
    'Demolition',
    'Concrete Work',
    'Roofing Helper',
    'Electrical Helper',
    'Plumbing Helper',
    'Drywall',
    'Flooring Installation',
    'Tile Work',
    'Carpentry',
    'Pressure Washing',
    'Fence Installation',
    'Deck Building',
  ]

  const tools = [
    'Basic Hand Tools',
    'Power Drill',
    'Circular Saw',
    'Hammer',
    'Level',
    'Measuring Tape',
    'Safety Equipment',
    'Ladder',
    'Wheelbarrow',
    'Shovel',
    'Rake',
    'Pressure Washer',
    'Paint Brushes/Rollers',
    'Screwdrivers',
    'Wrench Set',
    'Utility Knife',
  ]

  const handleSkillChange = (skill: string, checked: boolean) => {
    const updated = checked
      ? [...selectedSkills, skill]
      : selectedSkills.filter(s => s !== skill)
    
    setSelectedSkills(updated)
    setValue('skills', updated)
  }

  const handleToolChange = (tool: string, checked: boolean) => {
    const updated = checked
      ? [...selectedTools, tool]
      : selectedTools.filter(t => t !== tool)
    
    setSelectedTools(updated)
    setValue('tools_owned', updated)
  }

  const onSubmit = async (data: DayLaborerProfileFormData) => {
    setIsSubmitting(true)

    try {
      await updateDayLaborerProfile({
        ...data,
        skills: selectedSkills,
        tools_owned: selectedTools,
        availability: {
          monday: true,
          tuesday: true,
          wednesday: true,
          thursday: true,
          friday: true,
          saturday: true,
          sunday: false,
        },
        profile_completed: true,
      }).unwrap()

      dispatch(addNotification({
        type: 'success',
        message: 'Day laborer profile completed successfully!',
        duration: 5000,
      }))

      onComplete?.()
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to save profile. Please try again.',
        duration: 5000,
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center mb-4">
          <Wrench className="h-6 w-6 text-orange-600 dark:text-orange-400" />
        </div>
        <CardTitle className="text-2xl">Complete Your Day Laborer Profile</CardTitle>
        <CardDescription>
          Showcase your skills and availability to find work opportunities
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Skills */}
          <div className="space-y-3">
            <Label>Skills & Services</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {skills.map((skill) => (
                <div key={skill} className="flex items-center space-x-2">
                  <Checkbox
                    id={skill}
                    checked={selectedSkills.includes(skill)}
                    onCheckedChange={(checked) => 
                      handleSkillChange(skill, checked as boolean)
                    }
                  />
                  <Label htmlFor={skill} className="text-sm">
                    {skill}
                  </Label>
                </div>
              ))}
            </div>
            {errors.skills && (
              <p className="text-sm text-red-600">{errors.skills.message}</p>
            )}
          </div>

          {/* Hourly Rate */}
          <div className="space-y-2">
            <Label htmlFor="hourly_rate">Hourly Rate ($)</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="hourly_rate"
                type="number"
                placeholder="25"
                className="pl-10"
                {...register('hourly_rate', { valueAsNumber: true })}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Set your preferred hourly rate. You can adjust this later.
            </p>
            {errors.hourly_rate && (
              <p className="text-sm text-red-600">{errors.hourly_rate.message}</p>
            )}
          </div>

          {/* Transportation */}
          <div className="space-y-3">
            <Label>Transportation</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="transportation"
                checked={watch('transportation')}
                onCheckedChange={(checked) => setValue('transportation', checked as boolean)}
              />
              <Label htmlFor="transportation" className="text-sm flex items-center gap-2">
                <Car className="h-4 w-4" />
                I have reliable transportation
              </Label>
            </div>
            <p className="text-xs text-muted-foreground">
              Having transportation helps you access more job opportunities
            </p>
          </div>

          {/* Tools Owned */}
          <div className="space-y-3">
            <Label>Tools You Own (Optional)</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {tools.map((tool) => (
                <div key={tool} className="flex items-center space-x-2">
                  <Checkbox
                    id={tool}
                    checked={selectedTools.includes(tool)}
                    onCheckedChange={(checked) => 
                      handleToolChange(tool, checked as boolean)
                    }
                  />
                  <Label htmlFor={tool} className="text-sm">
                    {tool}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Listing your tools helps contractors know what equipment you can bring
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              className="flex-1"
              onClick={() => onComplete?.()}
            >
              Skip for Now
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving Profile...
                </>
              ) : (
                'Complete Profile'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
