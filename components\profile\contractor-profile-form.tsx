'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { useUpdateContractorProfileMutation } from '@/lib/store/api/profileApi'
import { useDispatch } from 'react-redux'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Briefcase, Building, Shield, DollarSign, MapPin, Loader2 } from 'lucide-react'

const contractorProfileSchema = z.object({
  business_name: z.string().min(2, 'Business name must be at least 2 characters'),
  license_number: z.string().optional(),
  insurance_verified: z.boolean().default(false),
  specializations: z.array(z.string()).min(1, 'Please select at least one specialization'),
  years_experience: z.number().min(0, 'Years of experience must be 0 or greater').max(50, 'Please enter a valid number'),
  service_radius: z.number().min(1, 'Service radius must be at least 1 mile').max(500, 'Please enter a valid radius'),
  hourly_rate: z.number().min(10, 'Hourly rate must be at least $10').max(1000, 'Please enter a valid rate'),
})

type ContractorProfileFormData = z.infer<typeof contractorProfileSchema>

interface ContractorProfileFormProps {
  onComplete?: () => void
  initialData?: Partial<ContractorProfileFormData>
}

export function ContractorProfileForm({ onComplete, initialData }: ContractorProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedSpecializations, setSelectedSpecializations] = useState<string[]>(
    initialData?.specializations || []
  )
  const dispatch = useDispatch()
  const [updateContractorProfile] = useUpdateContractorProfileMutation()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ContractorProfileFormData>({
    resolver: zodResolver(contractorProfileSchema),
    defaultValues: {
      ...initialData,
      specializations: selectedSpecializations,
    },
  })

  const specializations = [
    'General Contracting',
    'Electrical',
    'Plumbing',
    'HVAC',
    'Roofing',
    'Flooring',
    'Painting',
    'Kitchen Remodeling',
    'Bathroom Remodeling',
    'Landscaping',
    'Concrete Work',
    'Carpentry',
    'Drywall',
    'Insulation',
    'Windows & Doors',
    'Siding',
    'Tile Work',
    'Demolition',
  ]

  const handleSpecializationChange = (specialization: string, checked: boolean) => {
    const updated = checked
      ? [...selectedSpecializations, specialization]
      : selectedSpecializations.filter(s => s !== specialization)
    
    setSelectedSpecializations(updated)
    setValue('specializations', updated)
  }

  const onSubmit = async (data: ContractorProfileFormData) => {
    setIsSubmitting(true)

    try {
      await updateContractorProfile({
        ...data,
        specializations: selectedSpecializations,
        profile_completed: true,
      }).unwrap()

      dispatch(addNotification({
        type: 'success',
        message: 'Contractor profile completed successfully!',
        duration: 5000,
      }))

      onComplete?.()
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to save profile. Please try again.',
        duration: 5000,
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-emerald-100 dark:bg-emerald-900/20 rounded-full flex items-center justify-center mb-4">
          <Briefcase className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
        </div>
        <CardTitle className="text-2xl">Complete Your Contractor Profile</CardTitle>
        <CardDescription>
          Showcase your business and expertise to attract more clients
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Business Name */}
          <div className="space-y-2">
            <Label htmlFor="business_name">Business Name</Label>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="business_name"
                type="text"
                placeholder="Your Business Name"
                className="pl-10"
                {...register('business_name')}
              />
            </div>
            {errors.business_name && (
              <p className="text-sm text-red-600">{errors.business_name.message}</p>
            )}
          </div>

          {/* License Number */}
          <div className="space-y-2">
            <Label htmlFor="license_number">License Number (Optional)</Label>
            <div className="relative">
              <Shield className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="license_number"
                type="text"
                placeholder="License Number"
                className="pl-10"
                {...register('license_number')}
              />
            </div>
            {errors.license_number && (
              <p className="text-sm text-red-600">{errors.license_number.message}</p>
            )}
          </div>

          {/* Insurance Verification */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="insurance_verified"
              checked={watch('insurance_verified')}
              onCheckedChange={(checked) => setValue('insurance_verified', checked as boolean)}
            />
            <Label htmlFor="insurance_verified" className="text-sm">
              I have valid liability insurance
            </Label>
          </div>

          {/* Specializations */}
          <div className="space-y-3">
            <Label>Specializations</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {specializations.map((specialization) => (
                <div key={specialization} className="flex items-center space-x-2">
                  <Checkbox
                    id={specialization}
                    checked={selectedSpecializations.includes(specialization)}
                    onCheckedChange={(checked) => 
                      handleSpecializationChange(specialization, checked as boolean)
                    }
                  />
                  <Label htmlFor={specialization} className="text-sm">
                    {specialization}
                  </Label>
                </div>
              ))}
            </div>
            {errors.specializations && (
              <p className="text-sm text-red-600">{errors.specializations.message}</p>
            )}
          </div>

          {/* Experience and Rates */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="years_experience">Years of Experience</Label>
              <Input
                id="years_experience"
                type="number"
                placeholder="5"
                {...register('years_experience', { valueAsNumber: true })}
              />
              {errors.years_experience && (
                <p className="text-sm text-red-600">{errors.years_experience.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="service_radius">Service Radius (miles)</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="service_radius"
                  type="number"
                  placeholder="25"
                  className="pl-10"
                  {...register('service_radius', { valueAsNumber: true })}
                />
              </div>
              {errors.service_radius && (
                <p className="text-sm text-red-600">{errors.service_radius.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="hourly_rate">Hourly Rate ($)</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="hourly_rate"
                  type="number"
                  placeholder="75"
                  className="pl-10"
                  {...register('hourly_rate', { valueAsNumber: true })}
                />
              </div>
              {errors.hourly_rate && (
                <p className="text-sm text-red-600">{errors.hourly_rate.message}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              className="flex-1"
              onClick={() => onComplete?.()}
            >
              Skip for Now
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving Profile...
                </>
              ) : (
                'Complete Profile'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
