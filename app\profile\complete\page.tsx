"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import { setOnboardingStep } from "@/lib/store/slices/authSlice";
import { HomeownerProfileSteps } from "@/components/profile/homeowner-profile-steps";
import { ContractorProfileSteps } from "@/components/profile/contractor-profile-steps";
import { DayLaborerProfileForm } from "@/components/profile/day-laborer-profile-form";
import { CompletionIncentives } from "@/components/profile/completion-incentives";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  CheckCircle,
  Home,
  Briefcase,
  Hammer,
  Star,
  Users,
  TrendingUp,
  Shield,
  Clock,
  Sparkles,
  Award,
  Target,
  ChevronRight,
  Zap,
} from "lucide-react";
import { UserRole } from "@/lib/supabase/types";

export default function ProfileCompletePage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { user, selectedRoles, isAuthenticated, profileCompletionStatus } =
    useSelector((state: RootState) => state.auth);

  const [completedRoles, setCompletedRoles] = useState<UserRole[]>([]);
  const [showIncentives, setShowIncentives] = useState<
    Record<UserRole, boolean>
  >({});
  const [activeRole, setActiveRole] = useState<UserRole | null>(null);

  // Redirect if not authenticated or no roles selected
  useEffect(() => {
    if (!isAuthenticated || !selectedRoles || selectedRoles.length === 0) {
      router.push("/");
      return;
    }

    // Initialize incentives state for all selected roles
    const initialIncentives: Record<UserRole, boolean> = {};
    selectedRoles.forEach((role) => {
      initialIncentives[role] = true;
    });
    setShowIncentives(initialIncentives);

    // Set active role to first incomplete role
    const firstIncompleteRole = selectedRoles.find(
      (role) => !completedRoles.includes(role)
    );
    if (firstIncompleteRole) {
      setActiveRole(firstIncompleteRole);
    }
  }, [isAuthenticated, selectedRoles, router, completedRoles]);

  const handleRoleComplete = (role: UserRole) => {
    setCompletedRoles((prev) => [...prev, role]);

    // Check if all selected roles are completed
    const allCompleted = selectedRoles.every(
      (r) => completedRoles.includes(r) || r === role
    );

    if (allCompleted) {
      // All roles completed - redirect to general dashboard
      dispatch(setOnboardingStep("completed"));
      router.push("/general-dashboard");
    } else {
      // Move to next incomplete role
      const nextIncompleteRole = selectedRoles.find(
        (r) => !completedRoles.includes(r) && r !== role
      );
      if (nextIncompleteRole) {
        setActiveRole(nextIncompleteRole);
        setShowIncentives((prev) => ({ ...prev, [nextIncompleteRole]: true }));
      } else {
        // If no more incomplete roles, redirect to role-specific dashboard
        switch (role) {
          case "homeowner":
            router.push("/homeowner/dashboard");
            break;
          case "contractor":
            router.push("/contractor/dashboard");
            break;
          case "day_laborer":
            router.push("/day-laborer/dashboard");
            break;
          default:
            router.push("/general-dashboard");
        }
      }
    }
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case "homeowner":
        return <Home className="h-6 w-6" />;
      case "contractor":
        return <Briefcase className="h-6 w-6" />;
      case "day_laborer":
        return <Hammer className="h-6 w-6" />;
      default:
        return <Home className="h-6 w-6" />;
    }
  };

  const getRoleTitle = (role: UserRole) => {
    switch (role) {
      case "homeowner":
        return "Homeowner";
      case "contractor":
        return "Contractor";
      case "day_laborer":
        return "Day Laborer";
      default:
        return "Profile";
    }
  };

  const getRoleDescription = (role: UserRole) => {
    switch (role) {
      case "homeowner":
        return "Set up your homeowner profile to start posting projects and hiring professionals";
      case "contractor":
        return "Create your contractor profile to showcase your business and bid on projects";
      case "day_laborer":
        return "Build your day laborer profile to find flexible work opportunities";
      default:
        return "Complete your profile setup";
    }
  };

  const getRoleStats = (role: UserRole) => {
    switch (role) {
      case "homeowner":
        return {
          projects: "15,000+ projects posted",
          satisfaction: "4.8/5 satisfaction rate",
          savings: "Save up to 30% on costs",
        };
      case "contractor":
        return {
          contractors: "8,500+ active contractors",
          earnings: "Avg. $75k+ annual revenue",
          growth: "40% business growth",
        };
      case "day_laborer":
        return {
          laborers: "3,200+ day laborers",
          rate: "$25-45/hour average",
          flexibility: "Work when you want",
        };
      default:
        return {};
    }
  };

  const renderProfileForm = (role: UserRole) => {
    // Show incentives first
    if (showIncentives[role]) {
      return (
        <CompletionIncentives
          role={role}
          currentPercentage={profileCompletionStatus?.[role]?.percentage || 0}
          onContinue={() =>
            setShowIncentives((prev) => ({ ...prev, [role]: false }))
          }
          onSkip={() => {
            setShowIncentives((prev) => ({ ...prev, [role]: false }));
          }}
          showSkipOption={true}
        />
      );
    }

    // Show actual profile forms
    switch (role) {
      case "homeowner":
        return (
          <HomeownerProfileSteps
            onComplete={() => handleRoleComplete("homeowner")}
            onBack={() =>
              setShowIncentives((prev) => ({ ...prev, [role]: true }))
            }
          />
        );
      case "contractor":
        return (
          <ContractorProfileSteps
            onComplete={() => handleRoleComplete("contractor")}
            onBack={() =>
              setShowIncentives((prev) => ({ ...prev, [role]: true }))
            }
          />
        );
      case "day_laborer":
        return (
          <DayLaborerProfileForm
            onComplete={() => handleRoleComplete("day_laborer")}
          />
        );
      default:
        return null;
    }
  };

  if (!isAuthenticated || !selectedRoles || selectedRoles.length === 0) {
    return null;
  }

  const overallProgress = (completedRoles.length / selectedRoles.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-200/30 to-indigo-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <div className="relative bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/")}
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </div>

            {/* Progress Section */}
            <div className="flex items-center gap-6">
              <div className="text-right">
                <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Profile Setup Progress
                </div>
                <div className="text-xs text-slate-500 dark:text-slate-400">
                  {completedRoles.length} of {selectedRoles.length} completed
                </div>
              </div>
              <div className="w-32">
                <Progress value={overallProgress} className="h-2" />
              </div>
              <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                {Math.round(overallProgress)}%
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-800 dark:text-orange-200 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Sparkles className="h-4 w-4" />
            Almost there! Let's complete your profile
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4">
            Welcome to{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600">
              BuildPro
            </span>
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Complete your profile{selectedRoles.length > 1 ? "s" : ""} to unlock
            the full potential of our platform and start connecting with the
            right opportunities.
          </p>
        </div>

        {/* Role Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {selectedRoles.map((role) => {
            const isCompleted = completedRoles.includes(role);
            const isActive = activeRole === role;
            const stats = getRoleStats(role);

            return (
              <Card
                key={role}
                className={`relative overflow-hidden transition-all duration-300 cursor-pointer group ${
                  isCompleted
                    ? "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 border-emerald-200 dark:border-emerald-700 shadow-lg shadow-emerald-500/10"
                    : isActive
                    ? "bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-orange-200 dark:border-orange-700 shadow-lg shadow-orange-500/10 ring-2 ring-orange-500/20"
                    : "bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 hover:shadow-lg hover:border-orange-200 dark:hover:border-orange-700"
                }`}
                onClick={() => !isCompleted && setActiveRole(role)}
              >
                {/* Completion Badge */}
                {isCompleted && (
                  <div className="absolute top-4 right-4 bg-emerald-500 text-white rounded-full p-1">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                )}

                {/* Active Indicator */}
                {isActive && !isCompleted && (
                  <div className="absolute top-4 right-4 bg-orange-500 text-white rounded-full p-1 animate-pulse">
                    <Zap className="h-4 w-4" />
                  </div>
                )}

                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4">
                    <div
                      className={`p-3 rounded-xl ${
                        isCompleted
                          ? "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                          : isActive
                          ? "bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400"
                          : "bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400"
                      }`}
                    >
                      {getRoleIcon(role)}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg font-semibold">
                        {getRoleTitle(role)}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {isCompleted ? (
                          <span className="text-sm text-emerald-600 dark:text-emerald-400 font-medium">
                            ✓ Completed
                          </span>
                        ) : isActive ? (
                          <span className="text-sm text-orange-600 dark:text-orange-400 font-medium">
                            ● In Progress
                          </span>
                        ) : (
                          <span className="text-sm text-slate-500 dark:text-slate-400">
                            Pending
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-4">
                    {getRoleDescription(role)}
                  </p>

                  {/* Stats */}
                  <div className="space-y-2 text-xs text-slate-500 dark:text-slate-400">
                    {Object.entries(stats).map(([key, value]) => (
                      <div key={key} className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                        {value}
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                    {isCompleted ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowIncentives((prev) => ({
                            ...prev,
                            [role]: false,
                          }));
                          setActiveRole(role);
                        }}
                      >
                        Edit Profile
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        className={`w-full ${
                          isActive
                            ? "bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                            : "bg-slate-600 hover:bg-slate-700"
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveRole(role);
                        }}
                      >
                        {isActive ? "Continue Setup" : "Start Setup"}
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Active Profile Form */}
        {activeRole && (
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl">
            <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-lg">
                  {getRoleIcon(activeRole)}
                </div>
                <div>
                  <CardTitle className="text-2xl">
                    Complete Your {getRoleTitle(activeRole)} Profile
                  </CardTitle>
                  <CardDescription className="text-orange-100">
                    {getRoleDescription(activeRole)}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-8">
              {renderProfileForm(activeRole)}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
