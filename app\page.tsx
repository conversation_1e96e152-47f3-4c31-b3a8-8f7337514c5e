"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { HeroSection } from "@/components/home/<USER>";
import { CategorySection } from "@/components/home/<USER>";
import { PopularServices } from "@/components/home/<USER>";
import { FeaturedProjects } from "@/components/home/<USER>";
import { BecomeProvider } from "@/components/home/<USER>";
import { Testimonials } from "@/components/home/<USER>";
import { BlogSection } from "@/components/home/<USER>";
import { GeneralDashboard } from "@/components/dashboard/general-dashboard";

export default function Home() {
  const { isAuthenticated, onboardingStep } = useSelector(
    (state: RootState) => state.auth
  );

  // Show general dashboard for authenticated users in general-dashboard step
  if (isAuthenticated && onboardingStep === "general-dashboard") {
    return <GeneralDashboard />;
  }

  // Show regular landing page for non-authenticated users or completed onboarding
  return (
    <>
      <HeroSection />
      <CategorySection />
      <PopularServices />
      <FeaturedProjects />
      <BecomeProvider />
      <Testimonials />
      <BlogSection />
    </>
  );
}
