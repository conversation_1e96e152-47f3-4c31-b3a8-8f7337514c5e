"use client";

import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  CreateQuoteRequest,
  useCreateQuoteMutation,
} from "@/lib/store/api/quotesApi";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Plus,
  Trash2,
  DollarSign,
  Calendar,
  FileText,
  Calculator,
  Clock,
  AlertCircle,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

const quoteItemSchema = z.object({
  item_type: z.enum(["labor", "material", "equipment", "permit", "other"]),
  description: z.string().min(1, "Description is required"),
  quantity: z.number().min(0.01, "Quantity must be greater than 0"),
  unit_price: z.number().min(0.01, "Unit price must be greater than 0"),
});

const quoteSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  labor_cost: z.number().min(0, "Labor cost must be 0 or greater").optional(),
  material_cost: z
    .number()
    .min(0, "Material cost must be 0 or greater")
    .optional(),
  estimated_duration: z
    .number()
    .min(1, "Duration must be at least 1 day")
    .optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  notes: z.string().optional(),
  terms_and_conditions: z.string().optional(),
  valid_until: z.string().optional(),
  items: z.array(quoteItemSchema).optional(),
});

type QuoteFormData = z.infer<typeof quoteSchema>;

interface QuoteCreationFormProps {
  projectId: string;
  homeownerId: string;
  projectTitle?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function QuoteCreationForm({
  projectId,
  homeownerId,
  projectTitle,
  onSuccess,
  onCancel,
  open,
  onOpenChange,
}: QuoteCreationFormProps) {
  const [createQuote, { isLoading }] = useCreateQuoteMutation();
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();

  const form = useForm<QuoteFormData>({
    resolver: zodResolver(quoteSchema),
    defaultValues: {
      title: projectTitle ? `Quote for ${projectTitle}` : "",
      description: "",
      labor_cost: 0,
      material_cost: 0,
      estimated_duration: 1,
      start_date: "",
      end_date: "",
      notes: "",
      terms_and_conditions: "",
      valid_until: "",
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const watchedValues = form.watch();
  const totalAmount =
    (watchedValues.labor_cost || 0) +
    (watchedValues.material_cost || 0) +
    (watchedValues.items?.reduce(
      (sum, item) => sum + item.quantity * item.unit_price,
      0
    ) || 0);

  const addQuoteItem = () => {
    append({
      item_type: "material",
      description: "",
      quantity: 1,
      unit_price: 0,
    });
  };

  const onSubmit = async (data: QuoteFormData) => {
    try {
      const quoteData: CreateQuoteRequest = {
        project_id: projectId,
        homeowner_id: homeownerId,
        title: data.title,
        description: data.description,
        total_amount: totalAmount,
        labor_cost: data.labor_cost,
        material_cost: data.material_cost,
        estimated_duration: data.estimated_duration,
        start_date: data.start_date || undefined,
        end_date: data.end_date || undefined,
        notes: data.notes,
        terms_and_conditions: data.terms_and_conditions,
        valid_until: data.valid_until || undefined,
        items: data.items?.map((item) => ({
          ...item,
          total_price: item.quantity * item.unit_price,
        })),
      };

      await createQuote(quoteData).unwrap();

      toast({
        title: "Quote Created",
        description: "Your quote has been sent to the homeowner for review.",
      });

      form.reset();
      onSuccess?.();
      onOpenChange?.(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || "Failed to create quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const QuotePreview = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <span className="font-medium">Total Amount</span>
          <span className="text-2xl font-bold text-blue-600">
            {formatCurrency(totalAmount)}
          </span>
        </div>
        {(watchedValues.labor_cost || watchedValues.material_cost) && (
          <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
            {watchedValues.labor_cost && (
              <div className="flex justify-between">
                <span>Labor:</span>
                <span>{formatCurrency(watchedValues.labor_cost)}</span>
              </div>
            )}
            {watchedValues.material_cost && (
              <div className="flex justify-between">
                <span>Materials:</span>
                <span>{formatCurrency(watchedValues.material_cost)}</span>
              </div>
            )}
          </div>
        )}
      </div>

      {watchedValues.items && watchedValues.items.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium">Quote Items</h4>
          {watchedValues.items.map((item, index) => (
            <div
              key={index}
              className="flex justify-between p-2 bg-slate-50 rounded"
            >
              <div>
                <p className="font-medium text-sm">{item.description}</p>
                <p className="text-xs text-slate-600">
                  {item.quantity} × {formatCurrency(item.unit_price)}
                </p>
              </div>
              <span className="font-medium">
                {formatCurrency(item.quantity * item.unit_price)}
              </span>
            </div>
          ))}
        </div>
      )}

      {watchedValues.estimated_duration && (
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          <span>
            Estimated Duration: {watchedValues.estimated_duration} days
          </span>
        </div>
      )}

      {(watchedValues.start_date || watchedValues.end_date) && (
        <div className="flex items-center gap-4 text-sm">
          {watchedValues.start_date && (
            <span>
              Start: {new Date(watchedValues.start_date).toLocaleDateString()}
            </span>
          )}
          {watchedValues.end_date && (
            <span>
              End: {new Date(watchedValues.end_date).toLocaleDateString()}
            </span>
          )}
        </div>
      )}
    </div>
  );

  const formContent = (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Quote Information
          </CardTitle>
          <CardDescription>
            Provide basic information about your quote
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Quote Title *</Label>
            <Input
              id="title"
              placeholder="e.g., Kitchen Renovation Quote"
              {...form.register("title")}
            />
            {form.formState.errors.title && (
              <p className="text-sm text-red-600">
                {form.formState.errors.title.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe the work to be performed..."
              {...form.register("description")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Pricing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Pricing Breakdown
          </CardTitle>
          <CardDescription>
            Break down your costs for transparency
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="labor_cost">Labor Cost</Label>
              <Input
                id="labor_cost"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...form.register("labor_cost", { valueAsNumber: true })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="material_cost">Material Cost</Label>
              <Input
                id="material_cost"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...form.register("material_cost", { valueAsNumber: true })}
              />
            </div>
          </div>

          {/* Quote Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Additional Items</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addQuoteItem}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            {fields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-12 gap-2 items-end">
                <div className="col-span-2">
                  <Label>Type</Label>
                  <Select
                    value={form.watch(`items.${index}.item_type`)}
                    onValueChange={(value) =>
                      form.setValue(`items.${index}.item_type`, value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="labor">Labor</SelectItem>
                      <SelectItem value="material">Material</SelectItem>
                      <SelectItem value="equipment">Equipment</SelectItem>
                      <SelectItem value="permit">Permit</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-4">
                  <Label>Description</Label>
                  <Input
                    placeholder="Item description"
                    {...form.register(`items.${index}.description`)}
                  />
                </div>

                <div className="col-span-2">
                  <Label>Quantity</Label>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="1"
                    {...form.register(`items.${index}.quantity`, {
                      valueAsNumber: true,
                    })}
                  />
                </div>

                <div className="col-span-2">
                  <Label>Unit Price</Label>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...form.register(`items.${index}.unit_price`, {
                      valueAsNumber: true,
                    })}
                  />
                </div>

                <div className="col-span-1">
                  <Label>Total</Label>
                  <div className="h-10 flex items-center text-sm font-medium">
                    {formatCurrency(
                      (form.watch(`items.${index}.quantity`) || 0) *
                        (form.watch(`items.${index}.unit_price`) || 0)
                    )}
                  </div>
                </div>

                <div className="col-span-1">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Total Display */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">Total Quote Amount</span>
              <span className="text-2xl font-bold text-blue-600">
                {formatCurrency(totalAmount)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Timeline & Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="estimated_duration">
              Estimated Duration (days)
            </Label>
            <Input
              id="estimated_duration"
              type="number"
              min="1"
              placeholder="7"
              {...form.register("estimated_duration", { valueAsNumber: true })}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Proposed Start Date</Label>
              <Input
                id="start_date"
                type="date"
                {...form.register("start_date")}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">Proposed End Date</Label>
              <Input id="end_date" type="date" {...form.register("end_date")} />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="valid_until">Quote Valid Until</Label>
            <Input
              id="valid_until"
              type="date"
              {...form.register("valid_until")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Any additional notes or special considerations..."
              {...form.register("notes")}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="terms_and_conditions">Terms & Conditions</Label>
            <Textarea
              id="terms_and_conditions"
              placeholder="Payment terms, warranty information, etc..."
              {...form.register("terms_and_conditions")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={() => setShowPreview(true)}
          className="flex items-center gap-2"
        >
          <Calculator className="h-4 w-4" />
          Preview Quote
        </Button>

        <div className="flex gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading || totalAmount <= 0}
            className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
          >
            {isLoading ? "Creating..." : "Send Quote"}
          </Button>
        </div>
      </div>
    </form>
  );

  if (open !== undefined) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Quote</DialogTitle>
            <DialogDescription>
              Create a detailed quote for this project
            </DialogDescription>
          </DialogHeader>
          {formContent}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {formContent}

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Quote Preview</DialogTitle>
            <DialogDescription>
              Review your quote before sending
            </DialogDescription>
          </DialogHeader>
          <QuotePreview />
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
