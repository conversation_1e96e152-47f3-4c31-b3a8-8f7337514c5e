"use client";

import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import {
  useResendEmailVerificationMutation,
  useSignOutMutation,
} from "@/lib/store/api/authApi";
import { useGetProfileQuery } from "@/lib/store/api/profileApi";
import { addNotification } from "@/lib/store/slices/uiSlice";
import { Mail, CheckCircle, Clock, RefreshCw, LogOut } from "lucide-react";

interface EmailVerificationProps {
  onVerified?: () => void;
}

export function EmailVerification({ onVerified }: EmailVerificationProps) {
  const [isResending, setIsResending] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastSentAt, setLastSentAt] = useState<Date | null>(null);
  const dispatch = useDispatch();
  const { user, profile } = useSelector((state: RootState) => state.auth);
  const [resendEmailVerification] = useResendEmailVerificationMutation();
  const [signOut] = useSignOutMutation();
  const { refetch: refetchProfile } = useGetProfileQuery();

  const handleResendEmail = async () => {
    if (!user?.email) return;

    setIsResending(true);
    try {
      await resendEmailVerification({ email: user.email }).unwrap();
      setLastSentAt(new Date());
      dispatch(
        addNotification({
          type: "success",
          message: "Verification email sent! Please check your inbox.",
          duration: 5000,
        })
      );
    } catch (error: any) {
      dispatch(
        addNotification({
          type: "error",
          message:
            error.error ||
            "Failed to send verification email. Please try again.",
          duration: 5000,
        })
      );
    } finally {
      setIsResending(false);
    }
  };

  const canResend = !lastSentAt || Date.now() - lastSentAt.getTime() > 60000; // 1 minute cooldown

  const handleRefreshStatus = async () => {
    setIsRefreshing(true);
    try {
      await refetchProfile();
      dispatch(
        addNotification({
          type: "info",
          message: "Checking verification status...",
          duration: 2000,
        })
      );
    } catch (error) {
      console.error("Error refreshing profile:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut().unwrap();
      dispatch(
        addNotification({
          type: "info",
          message:
            "Signed out successfully. You can now sign in with a different account.",
          duration: 3000,
        })
      );
    } catch (error) {
      console.error("Error signing out:", error);
      dispatch(
        addNotification({
          type: "error",
          message: "Failed to sign out. Please try again.",
          duration: 3000,
        })
      );
    }
  };

  if (profile?.email_verified) {
    return (
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-emerald-100 dark:bg-emerald-900/20 rounded-full flex items-center justify-center">
          <CheckCircle className="h-8 w-8 text-emerald-600 dark:text-emerald-400" />
        </div>
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
            Email Verified!
          </h2>
          <p className="text-muted-foreground">
            Your email has been successfully verified. You can now continue
            setting up your account.
          </p>
        </div>
        <Button
          onClick={onVerified}
          className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
        >
          Continue Setup
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
        <Mail className="h-8 w-8 text-orange-600 dark:text-orange-400" />
      </div>

      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          Verify Your Email
        </h2>
        <p className="text-muted-foreground">
          We've sent a verification link to{" "}
          <span className="font-medium text-slate-900 dark:text-white">
            {user?.email}
          </span>
        </p>
      </div>

      <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 space-y-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          <span>Check your email and click the verification link</span>
        </div>
        <div className="text-xs text-muted-foreground">
          Don't forget to check your spam folder if you don't see the email in
          your inbox.
        </div>
      </div>

      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Already verified your email?
        </p>
        <Button
          variant="outline"
          onClick={handleRefreshStatus}
          disabled={isRefreshing}
          className="w-full"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              Checking...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Check Verification Status
            </>
          )}
        </Button>
      </div>

      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Didn't receive the email?
        </p>
        <Button
          variant="outline"
          onClick={handleResendEmail}
          disabled={isResending || !canResend}
          className="w-full"
        >
          {isResending ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              Sending...
            </>
          ) : (
            <>
              <Mail className="h-4 w-4 mr-2" />
              {canResend
                ? "Resend Verification Email"
                : `Wait ${Math.ceil(
                    (60000 - (Date.now() - (lastSentAt?.getTime() || 0))) / 1000
                  )}s`}
            </>
          )}
        </Button>
      </div>

      <div className="pt-4 border-t border-slate-200 dark:border-slate-700 space-y-3">
        <p className="text-xs text-muted-foreground">
          Having trouble? Contact our support team for assistance.
        </p>

        <div className="text-center">
          <Button
            variant="ghost"
            onClick={handleSignOut}
            className="text-muted-foreground hover:text-foreground text-sm"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign out and use different account
          </Button>
        </div>
      </div>
    </div>
  );
}
