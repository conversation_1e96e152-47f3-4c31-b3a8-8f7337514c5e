'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/store'
import { JobPostingForm } from '@/components/jobs/job-posting-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Home, CheckCircle, Users, MessageSquare } from 'lucide-react'

export default function PostJobPage() {
  const router = useRouter()
  const { isAuthenticated, selectedRoles, onboardingStep } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
      return
    }

    // Check if user has homeowner role
    if (!selectedRoles?.includes('homeowner')) {
      // Redirect to role selection to become a homeowner
      router.push('/?action=become-homeowner')
      return
    }

    // Check if onboarding is complete
    if (onboardingStep !== 'completed') {
      router.push('/profile/complete')
      return
    }
  }, [isAuthenticated, selectedRoles, onboardingStep, router])

  if (!isAuthenticated || !selectedRoles?.includes('homeowner') || onboardingStep !== 'completed') {
    return null
  }

  const handleSuccess = () => {
    router.push('/dashboard')
  }

  const handleCancel = () => {
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            
            <div className="text-sm text-muted-foreground">
              Post a Job
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar with Tips */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="text-lg">Tips for Success</CardTitle>
                <CardDescription>
                  Get better responses from contractors
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-emerald-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-sm">Be Detailed</h4>
                    <p className="text-xs text-muted-foreground">
                      Include specific materials, dimensions, and timeline preferences
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-sm">Set Clear Budget</h4>
                    <p className="text-xs text-muted-foreground">
                      Realistic budgets attract qualified contractors
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <MessageSquare className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-sm">Respond Quickly</h4>
                    <p className="text-xs text-muted-foreground">
                      Fast responses lead to better contractor engagement
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Home className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-sm">Add Photos</h4>
                    <p className="text-xs text-muted-foreground">
                      Visual context helps contractors provide accurate quotes
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Form */}
          <div className="lg:col-span-3">
            <JobPostingForm onSuccess={handleSuccess} onCancel={handleCancel} />
          </div>
        </div>
      </div>
    </div>
  )
}
