"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  Plus,
  Search,
  Star,
  Clock,
  DollarSign,
  Users,
  CheckCircle,
  AlertCircle,
  Calendar,
  MapPin,
  Phone,
  MessageSquare,
  Settings,
  Bell,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Filter,
  FileText,
} from "lucide-react";

export default function HomeownerDashboard() {
  const router = useRouter();
  const { user, selectedRoles, profileCompletionStatus, isAuthenticated } =
    useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes("homeowner")) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const isProfileComplete =
    profileCompletionStatus?.homeowner?.completed || false;

  // Mock data - in real app, this would come from API
  const mockProjects = [
    {
      id: "1",
      title: "Kitchen Renovation",
      status: "in_progress",
      budget: 25000,
      applicants: 8,
      deadline: "2024-02-15",
      contractor: "Mike's Construction",
    },
    {
      id: "2",
      title: "Bathroom Remodel",
      status: "completed",
      budget: 12000,
      applicants: 5,
      deadline: "2024-01-30",
      contractor: "Elite Renovations",
    },
    {
      id: "3",
      title: "Roof Repair",
      status: "pending",
      budget: 8000,
      applicants: 12,
      deadline: "2024-02-28",
      contractor: null,
    },
  ];

  const mockSavedContractors = [
    {
      id: "1",
      name: "Mike's Construction",
      rating: 4.9,
      specialties: ["Kitchen", "Bathroom"],
    },
    {
      id: "2",
      name: "Elite Renovations",
      rating: 4.8,
      specialties: ["Bathroom", "Flooring"],
    },
    {
      id: "3",
      name: "Pro Roofing",
      rating: 4.7,
      specialties: ["Roofing", "Gutters"],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "in_progress":
        return <Clock className="h-4 w-4" />;
      case "pending":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-blue-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg">
                  <Home className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Homeowner Dashboard
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Manage your home improvement projects
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button onClick={() => router.push("/homeowner/projects/create")}>
                <Plus className="h-4 w-4 mr-2" />
                Post Project
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/general-dashboard")}
              >
                Switch Role
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Active Projects</p>
                  <p className="text-3xl font-bold">
                    {
                      mockProjects.filter((p) => p.status !== "completed")
                        .length
                    }
                  </p>
                </div>
                <Home className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Completed Projects</p>
                  <p className="text-3xl font-bold">
                    {
                      mockProjects.filter((p) => p.status === "completed")
                        .length
                    }
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Total Spent</p>
                  <p className="text-3xl font-bold">$37K</p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Saved Contractors</p>
                  <p className="text-3xl font-bold">
                    {mockSavedContractors.length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Projects */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Your Projects</CardTitle>
                    <CardDescription>
                      Manage your home improvement projects
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button
                      onClick={() => router.push("/homeowner/projects/create")}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      New Project
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockProjects.map((project) => (
                    <div
                      key={project.id}
                      className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-slate-900 dark:text-white mb-1">
                            {project.title}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-300">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />$
                              {project.budget.toLocaleString()}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {project.applicants} applicants
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(project.deadline).toLocaleDateString()}
                            </div>
                          </div>
                          {project.contractor && (
                            <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1">
                              Contractor: {project.contractor}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge
                            className={`${getStatusColor(
                              project.status
                            )} flex items-center gap-1`}
                          >
                            {getStatusIcon(project.status)}
                            {project.status.replace("_", " ")}
                          </Badge>
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={() => router.push("/homeowner/projects/create")}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Post New Project
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/homeowner/contractors")}
                  className="w-full"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Find Contractors
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/homeowner/services")}
                  className="w-full"
                >
                  <Star className="h-4 w-4 mr-2" />
                  Browse Services
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/homeowner/quotes")}
                  className="w-full"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Quotes
                </Button>
              </CardContent>
            </Card>

            {/* Saved Contractors */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Saved Contractors</CardTitle>
                <CardDescription>Your favorite professionals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockSavedContractors.map((contractor) => (
                    <div
                      key={contractor.id}
                      className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg"
                    >
                      <div className="flex-1">
                        <p className="font-medium text-slate-900 dark:text-white">
                          {contractor.name}
                        </p>
                        <div className="flex items-center gap-1 text-sm text-slate-600 dark:text-slate-300">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          {contractor.rating}
                        </div>
                        <p className="text-xs text-slate-500 dark:text-slate-400">
                          {contractor.specialties.join(", ")}
                        </p>
                      </div>
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Phone className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Profile Completion */}
            {!isProfileComplete && (
              <Card className="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
                <CardHeader>
                  <CardTitle className="text-lg text-orange-800 dark:text-orange-200">
                    Complete Your Profile
                  </CardTitle>
                  <CardDescription className="text-orange-700 dark:text-orange-300">
                    Unlock all features and get better matches
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() =>
                      router.push("/profile/complete?role=homeowner")
                    }
                    className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
