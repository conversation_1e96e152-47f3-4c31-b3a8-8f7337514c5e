export type UserRole = 'homeowner' | 'contractor' | 'laborer';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  rating?: number;
  joinedAt: Date;
  location?: string;
  bio?: string;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  providerId: string;
  providerName: string;
  providerAvatar?: string;
  providerRating?: number;
  price: number;
  priceType: 'fixed' | 'hourly' | 'daily';
  category: string;
  subCategory?: string;
  tags: string[];
  deliveryTime: number;
  deliveryTimeUnit: 'hour' | 'day';
  completedJobs: number;
  images: string[];
  featured?: boolean;
  createdAt: Date;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  ownerId: string;
  ownerName: string;
  budget: {
    min: number;
    max: number;
  };
  category: string;
  subCategory?: string;
  location?: string;
  status: 'open' | 'in-progress' | 'completed';
  postedAt: Date;
  deadline?: Date;
  proposals: number;
  skills: string[];
  attachments?: string[];
}

export interface Category {
  id: string;
  name: string;
  icon: string;
  services: number;
  subCategories?: { id: string; name: string }[];
}

export interface Testimonial {
  id: string;
  text: string;
  authorName: string;
  authorRole: string;
  authorAvatar: string;
  rating: number;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  author: string;
  date: Date;
  image: string;
  slug: string;
}