import Link from "next/link";
import { Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function BecomeProvider() {
  return (
    <section className="section-padding bg-background">
      <div className="container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div className="order-2 lg:order-1">
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/3760529/pexels-photo-3760529.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Contractor working"
                className="rounded-3xl object-cover w-full h-[500px] shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-3xl"></div>

              {/* Floating stats */}
              <div className="absolute top-6 left-6">
                <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md border border-white/30 dark:border-slate-700/30 rounded-2xl p-4 text-center shadow-lg">
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    10k+
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-300">
                    Active Providers
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-8 order-1 lg:order-2">
            <div className="space-y-6">
              <h2 className="heading-2">Work Your Way</h2>
              <p className="body-large text-muted-foreground">
                Join thousands of contractors and day laborers earning on their
                own terms. We connect you with homeowners looking for your
                skills.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  title: "Build your reputation",
                  description:
                    "Showcase your work and collect reviews to grow your business",
                },
                {
                  title: "Get paid securely",
                  description:
                    "Our secure payment system ensures you get paid for your work",
                },
                {
                  title: "Choose your projects",
                  description:
                    "Take on work that fits your schedule and expertise",
                },
                {
                  title: "Set your own rates",
                  description:
                    "Charge what you're worth and adjust based on demand",
                },
              ].map((benefit, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="rounded-full bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 p-2 mt-1">
                    <Check className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">
                      {benefit.title}
                    </h3>
                    <p className="text-muted-foreground">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="pt-6">
              <Link href="/become-provider">
                <Button
                  size="lg"
                  className="px-10 py-4 text-lg font-semibold bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 rounded-xl transition-all duration-200 hover:shadow-lg"
                >
                  Become a Provider
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
