"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Briefcase,
  Plus,
  Search,
  Star,
  Clock,
  DollarSign,
  Users,
  CheckCircle,
  AlertCircle,
  Calendar,
  MapPin,
  Phone,
  MessageSquare,
  Settings,
  Bell,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Filter,
  Award,
  Wallet,
  BarChart3,
  FileText,
} from "lucide-react";

export default function ContractorDashboard() {
  const router = useRouter();
  const { user, selectedRoles, profileCompletionStatus, isAuthenticated } =
    useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes("contractor")) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const isProfileComplete =
    profileCompletionStatus?.contractor?.completed || false;

  // Mock data - in real app, this would come from API
  const mockBids = [
    {
      id: "1",
      projectTitle: "Kitchen Renovation",
      homeowner: "Sarah Johnson",
      bidAmount: 22000,
      status: "pending",
      submittedDate: "2024-01-15",
      deadline: "2024-02-15",
    },
    {
      id: "2",
      projectTitle: "Bathroom Remodel",
      homeowner: "Michael Chen",
      bidAmount: 8500,
      status: "accepted",
      submittedDate: "2024-01-10",
      deadline: "2024-02-10",
    },
    {
      id: "3",
      projectTitle: "Roof Repair",
      homeowner: "Lisa Davis",
      bidAmount: 6500,
      status: "rejected",
      submittedDate: "2024-01-12",
      deadline: "2024-02-28",
    },
  ];

  const mockActiveJobs = [
    {
      id: "1",
      title: "Bathroom Remodel",
      client: "Michael Chen",
      progress: 75,
      deadline: "2024-02-10",
    },
    {
      id: "2",
      title: "Deck Construction",
      client: "Robert Wilson",
      progress: 45,
      deadline: "2024-02-20",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "accepted":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-slate-100 text-slate-800 border-slate-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "accepted":
        return <CheckCircle className="h-4 w-4" />;
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "rejected":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                  <Briefcase className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Contractor Dashboard
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Manage your business and projects
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button onClick={() => router.push("/contractor/jobs")}>
                <Search className="h-4 w-4 mr-2" />
                Find Work
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/general-dashboard")}
              >
                Switch Role
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Active Bids</p>
                  <p className="text-3xl font-bold">
                    {mockBids.filter((b) => b.status === "pending").length}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Active Jobs</p>
                  <p className="text-3xl font-bold">{mockActiveJobs.length}</p>
                </div>
                <Briefcase className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Monthly Earnings</p>
                  <p className="text-3xl font-bold">$12.7K</p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Success Rate</p>
                  <p className="text-3xl font-bold">78%</p>
                </div>
                <Award className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Bids */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Bids</CardTitle>
                    <CardDescription>
                      Track your project applications
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button onClick={() => router.push("/contractor/jobs")}>
                      <Search className="h-4 w-4 mr-2" />
                      Find More Work
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockBids.map((bid) => (
                    <div
                      key={bid.id}
                      className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-slate-900 dark:text-white mb-1">
                            {bid.projectTitle}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-300">
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {bid.homeowner}
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />$
                              {bid.bidAmount.toLocaleString()}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              Due: {new Date(bid.deadline).toLocaleDateString()}
                            </div>
                          </div>
                          <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                            Submitted:{" "}
                            {new Date(bid.submittedDate).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge
                            className={`${getStatusColor(
                              bid.status
                            )} flex items-center gap-1`}
                          >
                            {getStatusIcon(bid.status)}
                            {bid.status}
                          </Badge>
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {bid.status === "pending" && (
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Active Jobs */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm mt-6">
              <CardHeader>
                <CardTitle>Active Jobs</CardTitle>
                <CardDescription>
                  Projects currently in progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockActiveJobs.map((job) => (
                    <div
                      key={job.id}
                      className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h3 className="font-semibold text-slate-900 dark:text-white">
                            {job.title}
                          </h3>
                          <p className="text-sm text-slate-600 dark:text-slate-300">
                            Client: {job.client}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            {job.progress}% Complete
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            Due: {new Date(job.deadline).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${job.progress}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={() => router.push("/contractor/jobs")}
                  className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Find Work
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/services/create")}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Service
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/finances")}
                  className="w-full"
                >
                  <Wallet className="h-4 w-4 mr-2" />
                  Finances
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/analytics")}
                  className="w-full"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/contractor/quotes")}
                  className="w-full"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Quotes
                </Button>
              </CardContent>
            </Card>

            {/* Performance Stats */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Performance</CardTitle>
                <CardDescription>Your business metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Response Rate
                    </span>
                    <span className="font-semibold text-slate-900 dark:text-white">
                      92%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Avg. Rating
                    </span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold text-slate-900 dark:text-white">
                        4.8
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Jobs Completed
                    </span>
                    <span className="font-semibold text-slate-900 dark:text-white">
                      28
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">
                      Repeat Clients
                    </span>
                    <span className="font-semibold text-slate-900 dark:text-white">
                      15%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Profile Completion */}
            {!isProfileComplete && (
              <Card className="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
                <CardHeader>
                  <CardTitle className="text-lg text-orange-800 dark:text-orange-200">
                    Complete Your Profile
                  </CardTitle>
                  <CardDescription className="text-orange-700 dark:text-orange-300">
                    Unlock premium features and get more visibility
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() =>
                      router.push("/profile/complete?role=contractor")
                    }
                    className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
