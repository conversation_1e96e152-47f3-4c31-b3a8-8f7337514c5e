"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Home, 
  Briefcase, 
  Hammer,
  ArrowRight,
  CheckCircle,
  Clock,
  Star,
  TrendingUp,
  Users,
  DollarSign,
  Settings,
  Bell,
  Search,
  Plus,
  Eye,
  BarChart3,
  Calendar,
  MapPin,
  Award,
  Shield,
  Zap
} from "lucide-react";

export default function GeneralDashboard() {
  const router = useRouter();
  const { user, selectedRoles, profileCompletionStatus, isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles || selectedRoles.length === 0) {
      router.push("/auth/role-selection");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "homeowner":
        return <Home className="h-8 w-8" />;
      case "contractor":
        return <Briefcase className="h-8 w-8" />;
      case "day_laborer":
        return <Hammer className="h-8 w-8" />;
      default:
        return <Star className="h-8 w-8" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "homeowner":
        return "from-blue-500 to-indigo-600";
      case "contractor":
        return "from-emerald-500 to-teal-600";
      case "day_laborer":
        return "from-orange-500 to-red-600";
      default:
        return "from-slate-500 to-slate-600";
    }
  };

  const getRoleTitle = (role: string) => {
    switch (role) {
      case "homeowner":
        return "Homeowner";
      case "contractor":
        return "Contractor";
      case "day_laborer":
        return "Day Laborer";
      default:
        return "Profile";
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case "homeowner":
        return "Post projects, hire professionals, and manage your home improvement needs";
      case "contractor":
        return "Find work, manage projects, and grow your contracting business";
      case "day_laborer":
        return "Find flexible work opportunities and build your reputation";
      default:
        return "Manage your profile and activities";
    }
  };

  const getRoleActions = (role: string) => {
    const isCompleted = profileCompletionStatus?.[role]?.completed || false;
    const percentage = profileCompletionStatus?.[role]?.percentage || 0;

    switch (role) {
      case "homeowner":
        return {
          primary: isCompleted ? "Post a Project" : percentage > 0 ? "Continue Setup" : "Start Setup",
          primaryAction: () => {
            if (isCompleted) {
              router.push("/homeowner/projects/create");
            } else {
              router.push("/profile/complete?role=homeowner");
            }
          },
          secondary: isCompleted ? "Browse Services" : "View Dashboard",
          secondaryAction: () => {
            if (isCompleted) {
              router.push("/homeowner/services");
            } else {
              router.push("/homeowner/dashboard");
            }
          },
          stats: [
            { label: "Active Projects", value: "3", icon: <BarChart3 className="h-4 w-4" /> },
            { label: "Saved Contractors", value: "12", icon: <Users className="h-4 w-4" /> },
            { label: "Total Spent", value: "$8,500", icon: <DollarSign className="h-4 w-4" /> }
          ]
        };
      case "contractor":
        return {
          primary: isCompleted ? "Find Work" : percentage > 0 ? "Continue Setup" : "Start Setup",
          primaryAction: () => {
            if (isCompleted) {
              router.push("/contractor/jobs");
            } else {
              router.push("/profile/complete?role=contractor");
            }
          },
          secondary: isCompleted ? "Create Service" : "View Dashboard",
          secondaryAction: () => {
            if (isCompleted) {
              router.push("/contractor/services/create");
            } else {
              router.push("/contractor/dashboard");
            }
          },
          stats: [
            { label: "Active Bids", value: "5", icon: <TrendingUp className="h-4 w-4" /> },
            { label: "Completed Jobs", value: "28", icon: <CheckCircle className="h-4 w-4" /> },
            { label: "Monthly Earnings", value: "$12,750", icon: <DollarSign className="h-4 w-4" /> }
          ]
        };
      case "day_laborer":
        return {
          primary: isCompleted ? "Find Work" : percentage > 0 ? "Continue Setup" : "Start Setup",
          primaryAction: () => {
            if (isCompleted) {
              router.push("/day-laborer/jobs");
            } else {
              router.push("/profile/complete?role=day_laborer");
            }
          },
          secondary: isCompleted ? "View Schedule" : "View Dashboard",
          secondaryAction: () => {
            if (isCompleted) {
              router.push("/day-laborer/schedule");
            } else {
              router.push("/day-laborer/dashboard");
            }
          },
          stats: [
            { label: "Hours This Week", value: "32", icon: <Clock className="h-4 w-4" /> },
            { label: "Jobs Completed", value: "15", icon: <CheckCircle className="h-4 w-4" /> },
            { label: "Weekly Earnings", value: "$1,280", icon: <DollarSign className="h-4 w-4" /> }
          ]
        };
      default:
        return {
          primary: "Get Started",
          primaryAction: () => router.push("/profile/complete"),
          secondary: "Learn More",
          secondaryAction: () => router.push("/about"),
          stats: []
        };
    }
  };

  const getCompletionStatus = (role: string) => {
    const isCompleted = profileCompletionStatus?.[role]?.completed || false;
    const percentage = profileCompletionStatus?.[role]?.percentage || 0;

    if (isCompleted) {
      return { status: "completed", text: "Profile Complete", color: "text-emerald-600" };
    } else if (percentage > 0) {
      return { status: "in-progress", text: `${percentage}% Complete`, color: "text-orange-600" };
    } else {
      return { status: "not-started", text: "Not Started", color: "text-slate-500" };
    }
  };

  if (!isAuthenticated || !selectedRoles) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg">
                  <BarChart3 className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Welcome back, {user?.user_metadata?.full_name || user?.email?.split('@')[0]}!
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    Choose your role to get started
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline" onClick={() => router.push("/settings")}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" onClick={() => router.push("/notifications")}>
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Welcome Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
            Your BuildPro Dashboard
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            Access all your tools and manage your activities across different roles. 
            Complete your profiles to unlock all features.
          </p>
        </div>

        {/* Role Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
          {selectedRoles.map((role) => {
            const actions = getRoleActions(role);
            const completion = getCompletionStatus(role);
            const percentage = profileCompletionStatus?.[role]?.percentage || 0;
            const isCompleted = profileCompletionStatus?.[role]?.completed || false;

            return (
              <Card key={role} className="relative overflow-hidden border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                {/* Header with gradient */}
                <div className={`h-2 bg-gradient-to-r ${getRoleColor(role)}`} />
                
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${getRoleColor(role)} text-white shadow-lg`}>
                      {getRoleIcon(role)}
                    </div>
                    <div className="text-right">
                      <Badge 
                        className={`${
                          isCompleted 
                            ? "bg-emerald-100 text-emerald-800 border-emerald-200" 
                            : percentage > 0 
                            ? "bg-orange-100 text-orange-800 border-orange-200"
                            : "bg-slate-100 text-slate-800 border-slate-200"
                        }`}
                      >
                        {isCompleted ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Complete
                          </>
                        ) : percentage > 0 ? (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            {percentage}%
                          </>
                        ) : (
                          "Not Started"
                        )}
                      </Badge>
                    </div>
                  </div>

                  <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">
                    {getRoleTitle(role)}
                  </CardTitle>
                  <CardDescription className="text-slate-600 dark:text-slate-300">
                    {getRoleDescription(role)}
                  </CardDescription>

                  {/* Progress Bar */}
                  {!isCompleted && (
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span className="text-slate-600 dark:text-slate-300">Profile Setup</span>
                        <span className={completion.color}>{completion.text}</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  )}
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    {actions.stats.map((stat, index) => (
                      <div key={index} className="text-center p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                        <div className="flex items-center justify-center mb-1">
                          <div className={`p-1 rounded bg-gradient-to-r ${getRoleColor(role)} text-white`}>
                            {stat.icon}
                          </div>
                        </div>
                        <div className="text-lg font-bold text-slate-900 dark:text-white">
                          {stat.value}
                        </div>
                        <div className="text-xs text-slate-600 dark:text-slate-300">
                          {stat.label}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <Button
                      onClick={actions.primaryAction}
                      className={`w-full bg-gradient-to-r ${getRoleColor(role)} hover:shadow-lg transition-all duration-300`}
                    >
                      {actions.primary}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                    <Button
                      variant="outline"
                      onClick={actions.secondaryAction}
                      className="w-full"
                    >
                      {actions.secondary}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-600" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common tasks and shortcuts across all your roles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/search")}
                className="h-20 flex-col gap-2"
              >
                <Search className="h-6 w-6" />
                <span className="text-sm">Search</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/messages")}
                className="h-20 flex-col gap-2"
              >
                <Bell className="h-6 w-6" />
                <span className="text-sm">Messages</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/calendar")}
                className="h-20 flex-col gap-2"
              >
                <Calendar className="h-6 w-6" />
                <span className="text-sm">Calendar</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/help")}
                className="h-20 flex-col gap-2"
              >
                <Award className="h-6 w-6" />
                <span className="text-sm">Help</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
