"use client";

import { useState } from "react";
import { Search, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export function HeroSection() {
  const [searchQuery, setSearchQuery] = useState("");
  const [location, setLocation] = useState("");

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20 section-padding">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]"></div>

      {/* Additional decorative elements */}
      <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-br from-teal-200/20 to-emerald-200/20 rounded-full blur-3xl"></div>

      <div className="container relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div className="space-y-8 lg:space-y-10">
            <div className="space-y-6">
              <h1 className="heading-1 max-w-2xl">
                Find the right{" "}
                <span className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-500 bg-clip-text text-transparent">
                  home service
                </span>{" "}
                professionals
              </h1>
              <p className="body-large text-muted-foreground max-w-xl">
                Connect with skilled contractors and day laborers for all your
                home improvement and maintenance needs.
              </p>
            </div>

            <div className="w-full max-w-2xl">
              <div className="flex flex-col sm:flex-row gap-3 p-2 bg-white dark:bg-gray-900 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="What service do you need?"
                    className="pl-12 pr-4 h-14 border-0 bg-transparent text-base focus-visible:ring-0 focus-visible:ring-offset-0"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="relative flex-1">
                  <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Enter location"
                    className="pl-12 pr-4 h-14 border-0 bg-transparent text-base focus-visible:ring-0 focus-visible:ring-offset-0"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                  />
                </div>
                <Button className="h-14 px-8 text-base font-semibold bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 transition-all duration-200 whitespace-nowrap">
                  Search
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="text-muted-foreground font-medium">
                Popular:
              </span>
              <div className="flex flex-wrap gap-2">
                {[
                  "Kitchen Remodeling",
                  "Plumbing",
                  "Lawn Care",
                  "House Painting",
                ].map((service, index) => (
                  <Button
                    key={service}
                    variant="outline"
                    size="sm"
                    className="h-8 px-3 text-xs font-medium bg-white/50 dark:bg-gray-800/50 border-gray-300 dark:border-gray-600 hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:border-orange-300 dark:hover:border-orange-600 transition-colors"
                  >
                    {service}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className="relative hidden lg:block">
            <div className="relative rounded-3xl overflow-hidden shadow-2xl card-hover">
              <img
                src="https://images.pexels.com/photos/3990359/pexels-photo-3990359.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Contractor working on home renovation"
                className="w-full h-[650px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

              {/* Floating stats card */}
              <div className="absolute top-6 right-6">
                <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md border border-white/30 dark:border-slate-700/30 rounded-2xl p-4 text-center shadow-lg">
                  <div className="text-2xl font-bold text-slate-900 dark:text-white">
                    4.9
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-300">
                    Rating
                  </div>
                </div>
              </div>

              {/* Provider card */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md border border-white/30 dark:border-slate-700/30 rounded-2xl p-6 shadow-xl">
                  <div className="flex items-start gap-4">
                    <img
                      src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                      alt="Profile"
                      className="w-16 h-16 rounded-full object-cover border-3 border-white shadow-lg"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-slate-900 dark:text-white">
                        Mike Johnson
                      </h3>
                      <p className="text-sm text-slate-600 dark:text-slate-300 mb-3">
                        Kitchen Renovation Specialist
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <svg
                              key={star}
                              className="w-4 h-4 text-yellow-500 fill-current"
                              viewBox="0 0 24 24"
                            >
                              <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
                            </svg>
                          ))}
                          <span className="text-sm ml-2 text-slate-700 dark:text-slate-200">
                            4.9 (28 jobs)
                          </span>
                        </div>
                        <Button
                          size="sm"
                          className="bg-orange-600 text-white hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600"
                        >
                          View Profile
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
