"use client";

import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { RootState } from "@/lib/store";
import { useGetMyJobPostingsQuery } from "@/lib/store/api/jobsApi";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Home,
  Briefcase,
  Hammer,
  Plus,
  Search,
  MessageSquare,
  Star,
  DollarSign,
  Calendar,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Eye,
  FileText,
  Settings,
} from "lucide-react";
import { UserRole } from "@/lib/supabase/types";

interface RoleDashboardProps {
  role: UserRole;
}

export function HomeownerDashboard({ role }: RoleDashboardProps) {
  const router = useRouter();
  const { profileCompletionStatus } = useSelector(
    (state: RootState) => state.auth
  );
  const completionPercentage = profileCompletionStatus?.[role]?.percentage || 0;

  // Get real job data
  const { data: jobs = [], isLoading: isLoadingJobs } =
    useGetMyJobPostingsQuery();

  // Calculate stats from real data
  const activeJobs = jobs.filter((job) => job.status === "active").length;
  const totalJobs = jobs.length;

  const mockProjects = [
    {
      id: 1,
      title: "Kitchen Renovation",
      status: "active",
      bids: 5,
      budget: "$15,000",
    },
    {
      id: 2,
      title: "Bathroom Remodel",
      status: "planning",
      bids: 0,
      budget: "$8,000",
    },
    {
      id: 3,
      title: "Deck Installation",
      status: "completed",
      bids: 3,
      budget: "$5,000",
    },
  ];

  const mockContractors = [
    {
      id: 1,
      name: "Mike Rodriguez",
      rating: 4.9,
      projects: 127,
      specialty: "Kitchen Renovation",
    },
    {
      id: 2,
      name: "Sarah Johnson",
      rating: 4.8,
      projects: 89,
      specialty: "Bathroom Remodel",
    },
    {
      id: 3,
      name: "David Chen",
      rating: 4.7,
      projects: 156,
      specialty: "General Contracting",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Homeowner Dashboard
          </h1>
          <p className="text-muted-foreground">
            Manage your projects and find trusted contractors
          </p>
        </div>
        <Button
          className="bg-gradient-to-r from-blue-600 to-indigo-600"
          onClick={() => router.push("/jobs/post")}
        >
          <Plus className="h-4 w-4 mr-2" />
          Post New Project
        </Button>
      </div>

      {/* Profile Completion Banner */}
      {completionPercentage < 100 && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <h3 className="font-semibold text-orange-900 dark:text-orange-100">
                    Complete Your Profile ({completionPercentage}%)
                  </h3>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    Get 3x better contractor matches by completing your profile
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Complete Now
              </Button>
            </div>
            <Progress value={completionPercentage} className="mt-3 h-2" />
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Projects</p>
                <p className="text-2xl font-bold">{activeJobs}</p>
              </div>
              <Home className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Bids</p>
                <p className="text-2xl font-bold">8</p>
              </div>
              <FileText className="h-8 w-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Response</p>
                <p className="text-2xl font-bold">4h</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Saved</p>
                <p className="text-2xl font-bold">$2.3K</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Projects */}
        <Card>
          <CardHeader>
            <CardTitle>My Projects</CardTitle>
            <CardDescription>
              Track your ongoing and completed projects
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {isLoadingJobs ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse p-3 border rounded-lg">
                    <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : jobs.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground mb-4">
                  No projects posted yet
                </p>
                <Button
                  onClick={() => router.push("/jobs/post")}
                  className="bg-gradient-to-r from-orange-600 to-red-600"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Post Your First Project
                </Button>
              </div>
            ) : (
              <>
                {jobs.slice(0, 3).map((job) => (
                  <div
                    key={job.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 cursor-pointer"
                    onClick={() => router.push(`/jobs/${job.id}`)}
                  >
                    <div>
                      <h4 className="font-medium">{job.title}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge
                          variant={
                            job.status === "active" ? "default" : "secondary"
                          }
                        >
                          {job.status.replace("_", " ")}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {job.category} •{" "}
                          {job.budget_type === "negotiable"
                            ? "Negotiable"
                            : job.budget_min && job.budget_max
                            ? `$${job.budget_min}-${job.budget_max}`
                            : "Budget TBD"}
                        </span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/jobs/my-jobs")}
                >
                  View All Projects ({totalJobs})
                </Button>
              </>
            )}
          </CardContent>
        </Card>

        {/* Recommended Contractors */}
        <Card>
          <CardHeader>
            <CardTitle>Recommended Contractors</CardTitle>
            <CardDescription>
              Top-rated contractors for your projects
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {mockContractors.map((contractor) => (
              <div
                key={contractor.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <h4 className="font-medium">{contractor.name}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm">{contractor.rating}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {contractor.projects} projects
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {contractor.specialty}
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Contact
                </Button>
              </div>
            ))}
            <Button variant="outline" className="w-full">
              Browse All Contractors
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex-col gap-2"
              onClick={() => router.push("/jobs/post")}
            >
              <Plus className="h-6 w-6" />
              <span>Post Project</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col gap-2"
              onClick={() => router.push("/contractors")}
            >
              <Search className="h-6 w-6" />
              <span>Find Contractors</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col gap-2"
              onClick={() => router.push("/messages")}
            >
              <MessageSquare className="h-6 w-6" />
              <span>Messages</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex-col gap-2"
              onClick={() => router.push("/profile/complete")}
            >
              <Settings className="h-6 w-6" />
              <span>Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function ContractorDashboard({ role }: RoleDashboardProps) {
  const { profileCompletionStatus } = useSelector(
    (state: RootState) => state.auth
  );
  const completionPercentage = profileCompletionStatus?.[role]?.percentage || 0;

  const mockLeads = [
    {
      id: 1,
      title: "Kitchen Renovation",
      location: "San Francisco",
      budget: "$15,000",
      posted: "2h ago",
    },
    {
      id: 2,
      title: "Bathroom Remodel",
      location: "Oakland",
      budget: "$8,000",
      posted: "4h ago",
    },
    {
      id: 3,
      title: "Deck Installation",
      location: "Berkeley",
      budget: "$5,000",
      posted: "1d ago",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Contractor Dashboard
          </h1>
          <p className="text-muted-foreground">
            Grow your business and find new opportunities
          </p>
        </div>
        <Button className="bg-gradient-to-r from-emerald-600 to-teal-600">
          <Search className="h-4 w-4 mr-2" />
          Browse Projects
        </Button>
      </div>

      {/* Profile Completion Banner */}
      {completionPercentage < 100 && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <h3 className="font-semibold text-orange-900 dark:text-orange-100">
                    Complete Your Business Profile ({completionPercentage}%)
                  </h3>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    Get 5x more project leads by completing your contractor
                    profile
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Complete Now
              </Button>
            </div>
            <Progress value={completionPercentage} className="mt-3 h-2" />
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Bids</p>
                <p className="text-2xl font-bold">12</p>
              </div>
              <FileText className="h-8 w-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Win Rate</p>
                <p className="text-2xl font-bold">68%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">$24K</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Rating</p>
                <p className="text-2xl font-bold">4.9</p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* New Leads */}
        <Card>
          <CardHeader>
            <CardTitle>New Project Leads</CardTitle>
            <CardDescription>
              Fresh opportunities matching your skills
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {mockLeads.map((lead) => (
              <div
                key={lead.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <h4 className="font-medium">{lead.title}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-muted-foreground">
                      {lead.location}
                    </span>
                    <span className="text-sm font-medium text-green-600">
                      {lead.budget}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">{lead.posted}</p>
                </div>
                <Button size="sm">Bid Now</Button>
              </div>
            ))}
            <Button variant="outline" className="w-full">
              View All Leads
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest bids and project updates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <CheckCircle className="h-5 w-5 text-emerald-600" />
                <div>
                  <p className="text-sm font-medium">
                    Bid accepted for Kitchen Renovation
                  </p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">
                    New message from Sarah Johnson
                  </p>
                  <p className="text-xs text-muted-foreground">4 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <Eye className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm font-medium">
                    Profile viewed 15 times today
                  </p>
                  <p className="text-xs text-muted-foreground">Today</p>
                </div>
              </div>
            </div>
            <Button variant="outline" className="w-full">
              View All Activity
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export function DayLaborerDashboard({ role }: RoleDashboardProps) {
  const { profileCompletionStatus } = useSelector(
    (state: RootState) => state.auth
  );
  const completionPercentage = profileCompletionStatus?.[role]?.percentage || 0;

  const mockGigs = [
    {
      id: 1,
      title: "Moving Help Needed",
      location: "Downtown",
      pay: "$25/hr",
      duration: "4 hours",
    },
    {
      id: 2,
      title: "Warehouse Loading",
      location: "Industrial District",
      pay: "$22/hr",
      duration: "8 hours",
    },
    {
      id: 3,
      title: "Event Setup",
      location: "Convention Center",
      pay: "$20/hr",
      duration: "6 hours",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Day Laborer Dashboard
          </h1>
          <p className="text-muted-foreground">
            Find flexible work opportunities that match your schedule
          </p>
        </div>
        <Button className="bg-gradient-to-r from-orange-600 to-red-600">
          <Search className="h-4 w-4 mr-2" />
          Find Gigs
        </Button>
      </div>

      {/* Profile Completion Banner */}
      {completionPercentage < 100 && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <h3 className="font-semibold text-orange-900 dark:text-orange-100">
                    Complete Your Worker Profile ({completionPercentage}%)
                  </h3>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    Get 3x more gig opportunities by completing your profile
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Complete Now
              </Button>
            </div>
            <Progress value={completionPercentage} className="mt-3 h-2" />
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Available Gigs</p>
                <p className="text-2xl font-bold">24</p>
              </div>
              <Hammer className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Week</p>
                <p className="text-2xl font-bold">32h</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Earnings</p>
                <p className="text-2xl font-bold">$840</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Rating</p>
                <p className="text-2xl font-bold">4.8</p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Available Gigs */}
        <Card>
          <CardHeader>
            <CardTitle>Available Gigs</CardTitle>
            <CardDescription>
              Gigs matching your skills and availability
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {mockGigs.map((gig) => (
              <div
                key={gig.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <h4 className="font-medium">{gig.title}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-muted-foreground">
                      {gig.location}
                    </span>
                    <span className="text-sm font-medium text-green-600">
                      {gig.pay}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {gig.duration}
                  </p>
                </div>
                <Button size="sm">Apply</Button>
              </div>
            ))}
            <Button variant="outline" className="w-full">
              View All Gigs
            </Button>
          </CardContent>
        </Card>

        {/* Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>Your Schedule</CardTitle>
            <CardDescription>Upcoming gigs and availability</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg bg-emerald-50 dark:bg-emerald-950/20">
                <div>
                  <p className="font-medium">Moving Help - Confirmed</p>
                  <p className="text-sm text-muted-foreground">
                    Tomorrow, 9:00 AM - 1:00 PM
                  </p>
                </div>
                <Badge className="bg-emerald-100 text-emerald-800">
                  Confirmed
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Available</p>
                  <p className="text-sm text-muted-foreground">
                    Friday, All Day
                  </p>
                </div>
                <Badge variant="outline">Open</Badge>
              </div>
            </div>
            <Button variant="outline" className="w-full mt-4">
              Manage Availability
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
