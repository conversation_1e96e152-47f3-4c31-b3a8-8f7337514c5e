"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  DollarSign, 
  Clock, 
  MapPin, 
  Star, 
  Upload,
  FileText,
  Plus,
  X,
  Eye,
  Save,
  Sparkles,
  TrendingUp,
  Users,
  Award,
  Target,
  CheckCircle
} from "lucide-react";

const serviceSchema = z.object({
  title: z.string().min(10, "Title must be at least 10 characters"),
  description: z.string().min(100, "Description must be at least 100 characters"),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().min(1, "Subcategory is required"),
  pricing: z.object({
    type: z.enum(["fixed", "hourly", "quote"]),
    amount: z.number().min(1, "Price is required"),
    unit: z.string().optional(),
  }),
  serviceArea: z.string().min(1, "Service area is required"),
  availability: z.string().min(1, "Availability is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
});

type ServiceData = z.infer<typeof serviceSchema>;

const categories = [
  {
    name: "Kitchen Renovation",
    subcategories: ["Full Renovation", "Cabinet Installation", "Countertops", "Backsplash", "Appliance Installation"]
  },
  {
    name: "Bathroom Renovation", 
    subcategories: ["Full Renovation", "Tile Work", "Plumbing", "Vanity Installation", "Shower Installation"]
  },
  {
    name: "Roofing",
    subcategories: ["Roof Replacement", "Roof Repair", "Gutter Installation", "Roof Inspection", "Emergency Repairs"]
  },
  {
    name: "Electrical",
    subcategories: ["Wiring", "Panel Upgrades", "Outlet Installation", "Lighting", "Smart Home"]
  },
  {
    name: "Plumbing",
    subcategories: ["Pipe Repair", "Fixture Installation", "Water Heater", "Drain Cleaning", "Emergency Plumbing"]
  }
];

export default function CreateServicePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceImages, setServiceImages] = useState<File[]>([]);
  const [currentTag, setCurrentTag] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");

  const form = useForm<ServiceData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      subcategory: "",
      pricing: {
        type: "fixed",
        amount: 0,
        unit: "",
      },
      serviceArea: "",
      availability: "",
      tags: [],
    },
  });

  const watchedTags = form.watch("tags");
  const watchedCategory = form.watch("category");

  const onSubmit = async (data: ServiceData) => {
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log("Service created:", data);
    console.log("Service images:", serviceImages);
    
    setIsSubmitting(false);
    router.push('/contractor/services?created=true');
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setServiceImages(prev => [...prev, ...files]);
  };

  const removeImage = (index: number) => {
    setServiceImages(prev => prev.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (currentTag.trim() && !watchedTags.includes(currentTag.trim())) {
      form.setValue("tags", [...watchedTags, currentTag.trim()]);
      setCurrentTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    form.setValue("tags", watchedTags.filter(tag => tag !== tagToRemove));
  };

  const selectedCategoryData = categories.find(cat => cat.name === watchedCategory);

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Services
              </Button>
            </div>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg">
                <Plus className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white">Create Service Listing</h1>
                <p className="text-sm text-slate-600 dark:text-slate-300">Showcase your expertise</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Service Details</CardTitle>
                <CardDescription>
                  Create a compelling service listing to attract potential clients.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Service Title</Label>
                      <Input
                        id="title"
                        placeholder="e.g., Professional Kitchen Renovation Services"
                        {...form.register("title")}
                        className="text-lg"
                      />
                      {form.formState.errors.title && (
                        <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Describe your service in detail. Include your experience, approach, and what makes you unique..."
                        rows={6}
                        {...form.register("description")}
                      />
                      {form.formState.errors.description && (
                        <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Category Selection */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <select
                        id="category"
                        {...form.register("category")}
                        onChange={(e) => {
                          form.setValue("category", e.target.value);
                          form.setValue("subcategory", ""); // Reset subcategory
                        }}
                        className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      >
                        <option value="">Select a category</option>
                        {categories.map((cat) => (
                          <option key={cat.name} value={cat.name}>
                            {cat.name}
                          </option>
                        ))}
                      </select>
                      {form.formState.errors.category && (
                        <p className="text-sm text-red-600">{form.formState.errors.category.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subcategory">Subcategory</Label>
                      <select
                        id="subcategory"
                        {...form.register("subcategory")}
                        disabled={!selectedCategoryData}
                        className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-slate-100"
                      >
                        <option value="">Select a subcategory</option>
                        {selectedCategoryData?.subcategories.map((subcat) => (
                          <option key={subcat} value={subcat}>
                            {subcat}
                          </option>
                        ))}
                      </select>
                      {form.formState.errors.subcategory && (
                        <p className="text-sm text-red-600">{form.formState.errors.subcategory.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="space-y-4">
                    <Label>Pricing</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="pricing-type">Pricing Type</Label>
                        <select
                          id="pricing-type"
                          {...form.register("pricing.type")}
                          className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        >
                          <option value="fixed">Fixed Price</option>
                          <option value="hourly">Hourly Rate</option>
                          <option value="quote">Custom Quote</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="pricing-amount">Amount ($)</Label>
                        <Input
                          id="pricing-amount"
                          type="number"
                          placeholder="0"
                          {...form.register("pricing.amount", { valueAsNumber: true })}
                        />
                        {form.formState.errors.pricing?.amount && (
                          <p className="text-sm text-red-600">{form.formState.errors.pricing.amount.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="pricing-unit">Unit (optional)</Label>
                        <Input
                          id="pricing-unit"
                          placeholder="e.g., per sq ft, per hour"
                          {...form.register("pricing.unit")}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Service Area & Availability */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="serviceArea">Service Area</Label>
                      <Input
                        id="serviceArea"
                        placeholder="e.g., San Francisco Bay Area, 25 mile radius"
                        {...form.register("serviceArea")}
                      />
                      {form.formState.errors.serviceArea && (
                        <p className="text-sm text-red-600">{form.formState.errors.serviceArea.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="availability">Availability</Label>
                      <Input
                        id="availability"
                        placeholder="e.g., Available weekdays, 2-week lead time"
                        {...form.register("availability")}
                      />
                      {form.formState.errors.availability && (
                        <p className="text-sm text-red-600">{form.formState.errors.availability.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="space-y-4">
                    <Label>Tags</Label>
                    <div className="flex gap-2">
                      <Input
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        placeholder="Add a tag..."
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button type="button" onClick={addTag} variant="outline">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {watchedTags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {watchedTags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                    {form.formState.errors.tags && (
                      <p className="text-sm text-red-600">{form.formState.errors.tags.message}</p>
                    )}
                  </div>

                  {/* Image Upload */}
                  <div className="space-y-4">
                    <Label>Service Images</Label>
                    <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 mx-auto text-slate-400 mb-2" />
                      <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                        Upload photos of your work to showcase your expertise
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="service-images"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('service-images')?.click()}
                      >
                        Choose Images
                      </Button>
                    </div>

                    {serviceImages.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {serviceImages.map((file, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={`Service ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                      className="sm:w-auto"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save as Draft
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Publish Service
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Preview & Tips */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Tips Card */}
              <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-emerald-800 dark:text-emerald-200">
                    <Target className="h-5 w-5" />
                    Tips for Success
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-4 w-4 text-emerald-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-emerald-800 dark:text-emerald-200">Clear Title</p>
                        <p className="text-xs text-emerald-700 dark:text-emerald-300">Use specific, descriptive titles</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-4 w-4 text-emerald-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-emerald-800 dark:text-emerald-200">Quality Photos</p>
                        <p className="text-xs text-emerald-700 dark:text-emerald-300">Show your best work examples</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-4 w-4 text-emerald-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-emerald-800 dark:text-emerald-200">Competitive Pricing</p>
                        <p className="text-xs text-emerald-700 dark:text-emerald-300">Research market rates</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-4 w-4 text-emerald-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-emerald-800 dark:text-emerald-200">Detailed Description</p>
                        <p className="text-xs text-emerald-700 dark:text-emerald-300">Explain your process and expertise</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Stats Card */}
              <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-emerald-600" />
                    Platform Stats
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">12,500+</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Active Homeowners</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">$2.3M</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Projects Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">4.9★</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Average Rating</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
