"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import { openSignUpModal, openSignInModal } from "@/lib/store/slices/uiSlice";
import { useSignOutMutation } from "@/lib/store/api/authApi";
import {
  Search,
  Menu,
  X,
  User,
  Home,
  MessageSquare,
  BriefcaseBusiness,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const dispatch = useDispatch();

  const { isAuthenticated, user, profile } = useSelector(
    (state: RootState) => state.auth
  );
  const [signOut] = useSignOutMutation();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  const handleSignOut = async () => {
    try {
      await signOut().unwrap();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 dark:border-gray-800 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md supports-[backdrop-filter]:bg-white/80 dark:supports-[backdrop-filter]:bg-gray-900/80">
      <div className="container flex h-16 lg:h-20 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="p-2 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl group-hover:from-orange-700 group-hover:to-red-700 transition-all duration-200">
              <Home className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              BuildPro
            </span>
          </Link>
          <nav className="hidden lg:flex items-center gap-8">
            <Link
              href="/services"
              className="text-sm font-semibold text-slate-700 dark:text-slate-300 hover:text-orange-600 dark:hover:text-orange-400 transition-colors relative group"
            >
              Services
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-600 group-hover:w-full transition-all duration-200"></span>
            </Link>
            <Link
              href="/projects"
              className="text-sm font-semibold text-slate-700 dark:text-slate-300 hover:text-orange-600 dark:hover:text-orange-400 transition-colors relative group"
            >
              Projects
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-600 group-hover:w-full transition-all duration-200"></span>
            </Link>
            <Link
              href="/contractors"
              className="text-sm font-semibold text-slate-700 dark:text-slate-300 hover:text-orange-600 dark:hover:text-orange-400 transition-colors relative group"
            >
              Contractors
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-600 group-hover:w-full transition-all duration-200"></span>
            </Link>
            <Link
              href="/how-it-works"
              className="text-sm font-semibold text-slate-700 dark:text-slate-300 hover:text-orange-600 dark:hover:text-orange-400 transition-colors relative group"
            >
              How It Works
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-600 group-hover:w-full transition-all duration-200"></span>
            </Link>
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <div
            className={`${
              isSearchOpen ? "flex" : "hidden"
            } lg:flex items-center relative`}
          >
            <div className="relative">
              <Input
                type="search"
                placeholder="Search services..."
                className="w-[280px] lg:w-[320px] pl-10 pr-4 h-10 bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-xl"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
          </div>
          <button
            className="lg:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            onClick={toggleSearch}
            aria-label="Toggle search"
          >
            <Search className="h-5 w-5" />
          </button>

          {isAuthenticated ? (
            <div className="hidden lg:flex items-center gap-3">
              <Link href="/messages">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-orange-50 dark:hover:bg-orange-900/20"
                  aria-label="Messages"
                >
                  <MessageSquare className="h-5 w-5" />
                </Button>
              </Link>
              <Link href="/dashboard">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-orange-50 dark:hover:bg-orange-900/20"
                  aria-label="Dashboard"
                >
                  <BriefcaseBusiness className="h-5 w-5" />
                </Button>
              </Link>
              <Link href="/profile">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 h-10 px-4 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                >
                  <User className="h-4 w-4" />
                  {profile?.full_name || "Profile"}
                </Button>
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleSignOut}
                className="h-10 w-10 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                aria-label="Sign out"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          ) : (
            <div className="hidden lg:flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => dispatch(openSignInModal())}
                className="h-10 px-6 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 font-semibold"
              >
                Sign In
              </Button>
              <Button
                size="sm"
                onClick={() => dispatch(openSignUpModal())}
                className="h-10 px-6 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 font-semibold rounded-xl"
              >
                Join Now
              </Button>
            </div>
          )}

          <button
            className="lg:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden px-4 py-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-lg">
          <nav className="flex flex-col space-y-1">
            <Link
              href="/services"
              className="text-base font-semibold px-4 py-3 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Services
            </Link>
            <Link
              href="/projects"
              className="text-base font-semibold px-4 py-3 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Projects
            </Link>
            <Link
              href="/contractors"
              className="text-base font-semibold px-4 py-3 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Contractors
            </Link>
            <Link
              href="/how-it-works"
              className="text-base font-semibold px-4 py-3 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              How It Works
            </Link>
            <div className="pt-4 mt-4 border-t border-slate-200 dark:border-slate-700">
              {isAuthenticated ? (
                <div className="space-y-1">
                  <Link
                    href="/profile"
                    className="flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300 transition-colors"
                  >
                    <User className="h-5 w-5" />
                    {profile?.full_name || "Profile"}
                  </Link>
                  <Link
                    href="/dashboard"
                    className="flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300 transition-colors"
                  >
                    <BriefcaseBusiness className="h-5 w-5" />
                    Dashboard
                  </Link>
                  <Link
                    href="/messages"
                    className="flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-800 text-slate-700 dark:text-slate-300 transition-colors"
                  >
                    <MessageSquare className="h-5 w-5" />
                    Messages
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="flex items-center gap-3 px-4 py-3 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 transition-colors w-full text-left"
                  >
                    <LogOut className="h-5 w-5" />
                    Sign Out
                  </button>
                </div>
              ) : (
                <div className="flex flex-col space-y-3">
                  <Button
                    variant="outline"
                    onClick={() => {
                      dispatch(openSignInModal());
                      setIsMenuOpen(false);
                    }}
                    className="w-full h-12 text-base font-semibold border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                  >
                    Sign In
                  </Button>
                  <Button
                    onClick={() => {
                      dispatch(openSignUpModal());
                      setIsMenuOpen(false);
                    }}
                    className="w-full h-12 text-base font-semibold bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-xl"
                  >
                    Join Now
                  </Button>
                </div>
              )}
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
