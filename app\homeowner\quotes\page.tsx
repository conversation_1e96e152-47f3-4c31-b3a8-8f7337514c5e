"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { useGetQuotesQuery } from "@/lib/store/api/quotesApi";
import { QuoteReviewCard } from "@/components/quotes/quote-review-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Home,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Filter,
  Search,
  ArrowLeft,
} from "lucide-react";

export default function HomeownerQuotesPage() {
  const router = useRouter();
  const { user, selectedRoles, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const { data: quotes = [], isLoading, error } = useGetQuotesQuery({ role: 'homeowner' });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes('homeowner')) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const pendingQuotes = quotes.filter(q => q.status === 'pending');
  const acceptedQuotes = quotes.filter(q => q.status === 'accepted');
  const rejectedQuotes = quotes.filter(q => q.status === 'rejected');
  const expiredQuotes = quotes.filter(q => q.status === 'expired' || (q.valid_until && new Date(q.valid_until) < new Date()));

  const getStatusCount = (status: string) => {
    switch (status) {
      case 'pending':
        return pendingQuotes.length;
      case 'accepted':
        return acceptedQuotes.length;
      case 'rejected':
        return rejectedQuotes.length;
      case 'expired':
        return expiredQuotes.length;
      default:
        return 0;
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-blue-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push("/homeowner/dashboard")}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg">
                  <FileText className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Quote Management</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Review and manage quotes from contractors</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button onClick={() => router.push("/homeowner/projects/create")}>
                <Plus className="h-4 w-4 mr-2" />
                Post New Project
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-yellow-500 to-orange-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100">Pending Review</p>
                  <p className="text-3xl font-bold">{getStatusCount('pending')}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Accepted</p>
                  <p className="text-3xl font-bold">{getStatusCount('accepted')}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100">Rejected</p>
                  <p className="text-3xl font-bold">{getStatusCount('rejected')}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-gray-500 to-slate-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-100">Expired</p>
                  <p className="text-3xl font-bold">{getStatusCount('expired')}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-gray-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quotes Tabs */}
        <Tabs defaultValue="pending" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pending ({getStatusCount('pending')})
            </TabsTrigger>
            <TabsTrigger value="accepted" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Accepted ({getStatusCount('accepted')})
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex items-center gap-2">
              <XCircle className="h-4 w-4" />
              Rejected ({getStatusCount('rejected')})
            </TabsTrigger>
            <TabsTrigger value="expired" className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Expired ({getStatusCount('expired')})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-6">
            {isLoading ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-6 bg-slate-200 rounded w-3/4"></div>
                      <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="h-4 bg-slate-200 rounded"></div>
                        <div className="h-4 bg-slate-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : pendingQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {pendingQuotes.map((quote) => (
                  <QuoteReviewCard
                    key={quote.id}
                    quote={quote}
                    contractorName="Contractor Name" // TODO: Fetch from contractor profile
                    projectTitle="Project Title" // TODO: Fetch from project
                  />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <Clock className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">No Pending Quotes</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-6">
                    You don't have any quotes waiting for review. Post a project to receive quotes from contractors.
                  </p>
                  <Button onClick={() => router.push("/homeowner/projects/create")}>
                    <Plus className="h-4 w-4 mr-2" />
                    Post New Project
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="accepted" className="space-y-6">
            {acceptedQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {acceptedQuotes.map((quote) => (
                  <QuoteReviewCard
                    key={quote.id}
                    quote={quote}
                    contractorName="Contractor Name"
                    projectTitle="Project Title"
                  />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <CheckCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">No Accepted Quotes</h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You haven't accepted any quotes yet.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="rejected" className="space-y-6">
            {rejectedQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {rejectedQuotes.map((quote) => (
                  <QuoteReviewCard
                    key={quote.id}
                    quote={quote}
                    contractorName="Contractor Name"
                    projectTitle="Project Title"
                  />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <XCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">No Rejected Quotes</h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You haven't rejected any quotes.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="expired" className="space-y-6">
            {expiredQuotes.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {expiredQuotes.map((quote) => (
                  <QuoteReviewCard
                    key={quote.id}
                    quote={quote}
                    contractorName="Contractor Name"
                    projectTitle="Project Title"
                  />
                ))}
              </div>
            ) : (
              <Card className="text-center py-12">
                <CardContent>
                  <AlertCircle className="h-16 w-16 mx-auto mb-4 text-slate-400" />
                  <h3 className="text-xl font-semibold mb-2">No Expired Quotes</h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    You don't have any expired quotes.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
