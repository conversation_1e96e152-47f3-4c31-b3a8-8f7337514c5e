"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Hammer, 
  Plus,
  Search,
  Star,
  Clock,
  DollarSign,
  Users,
  CheckCircle,
  AlertCircle,
  Calendar,
  MapPin,
  Phone,
  MessageSquare,
  Settings,
  Bell,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Filter,
  Award,
  Wallet,
  BarChart3,
  Timer,
  Target
} from "lucide-react";

export default function DayLaborerDashboard() {
  const router = useRouter();
  const { user, selectedRoles, profileCompletionStatus, isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login");
      return;
    }

    if (!selectedRoles?.includes('day_laborer')) {
      router.push("/general-dashboard");
      return;
    }
  }, [isAuthenticated, selectedRoles, router]);

  const isProfileComplete = profileCompletionStatus?.day_laborer?.completed || false;

  // Mock data - in real app, this would come from API
  const mockJobs = [
    {
      id: "1",
      title: "Moving Help",
      client: "Sarah Johnson",
      hourlyRate: 25,
      duration: 4,
      status: "confirmed",
      date: "2024-01-20",
      location: "Downtown"
    },
    {
      id: "2", 
      title: "Yard Cleanup",
      client: "Michael Chen",
      hourlyRate: 20,
      duration: 6,
      status: "pending",
      date: "2024-01-22",
      location: "Suburbs"
    },
    {
      id: "3",
      title: "Furniture Assembly",
      client: "Lisa Davis",
      hourlyRate: 30,
      duration: 3,
      status: "completed",
      date: "2024-01-18",
      location: "North Side"
    }
  ];

  const mockUpcomingJobs = [
    { id: "1", title: "Moving Help", client: "Sarah Johnson", time: "9:00 AM", date: "2024-01-20" },
    { id: "2", title: "Garden Work", client: "Robert Wilson", time: "2:00 PM", date: "2024-01-21" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'confirmed':
        return <Clock className="h-4 w-4" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-orange-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg">
                  <Hammer className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">Day Laborer Dashboard</h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">Find flexible work opportunities</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button onClick={() => router.push("/day-laborer/jobs")}>
                <Search className="h-4 w-4 mr-2" />
                Find Work
              </Button>
              <Button variant="outline" onClick={() => router.push("/general-dashboard")}>
                Switch Role
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Hours This Week</p>
                  <p className="text-3xl font-bold">32</p>
                </div>
                <Timer className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-100">Jobs Completed</p>
                  <p className="text-3xl font-bold">{mockJobs.filter(j => j.status === 'completed').length}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-emerald-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Weekly Earnings</p>
                  <p className="text-3xl font-bold">$1,280</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-pink-600 text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Success Rate</p>
                  <p className="text-3xl font-bold">95%</p>
                </div>
                <Target className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Jobs */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Jobs</CardTitle>
                    <CardDescription>Your job history and applications</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button onClick={() => router.push("/day-laborer/jobs")}>
                      <Search className="h-4 w-4 mr-2" />
                      Find More Work
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockJobs.map((job) => (
                    <div key={job.id} className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-slate-900 dark:text-white mb-1">
                            {job.title}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-300">
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {job.client}
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              ${job.hourlyRate}/hr
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {job.duration}h
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="h-4 w-4" />
                              {job.location}
                            </div>
                          </div>
                          <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                            Date: {new Date(job.date).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge className={`${getStatusColor(job.status)} flex items-center gap-1`}>
                            {getStatusIcon(job.status)}
                            {job.status}
                          </Badge>
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {job.status === 'pending' && (
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Schedule */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm mt-6">
              <CardHeader>
                <CardTitle>Upcoming Schedule</CardTitle>
                <CardDescription>Your confirmed jobs for this week</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockUpcomingJobs.map((job) => (
                    <div key={job.id} className="p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-slate-900 dark:text-white">
                            {job.title}
                          </h3>
                          <p className="text-sm text-slate-600 dark:text-slate-300">
                            Client: {job.client}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            {job.time}
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            {new Date(job.date).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={() => router.push("/day-laborer/jobs")}
                  className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Find Work
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push("/day-laborer/schedule")}
                  className="w-full"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  View Schedule
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push("/day-laborer/earnings")}
                  className="w-full"
                >
                  <Wallet className="h-4 w-4 mr-2" />
                  Earnings
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => router.push("/day-laborer/profile")}
                  className="w-full"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Profile
                </Button>
              </CardContent>
            </Card>

            {/* Performance Stats */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Performance</CardTitle>
                <CardDescription>Your work statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Completion Rate</span>
                    <span className="font-semibold text-slate-900 dark:text-white">95%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Avg. Rating</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold text-slate-900 dark:text-white">4.9</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Total Jobs</span>
                    <span className="font-semibold text-slate-900 dark:text-white">47</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Repeat Clients</span>
                    <span className="font-semibold text-slate-900 dark:text-white">25%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Availability Status */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Availability</CardTitle>
                <CardDescription>Your current work status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Status</span>
                    <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Available
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600 dark:text-slate-300">Next Available</span>
                    <span className="font-semibold text-slate-900 dark:text-white">Today</span>
                  </div>
                  <Button variant="outline" className="w-full">
                    Update Availability
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Profile Completion */}
            {!isProfileComplete && (
              <Card className="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
                <CardHeader>
                  <CardTitle className="text-lg text-orange-800 dark:text-orange-200">
                    Complete Your Profile
                  </CardTitle>
                  <CardDescription className="text-orange-700 dark:text-orange-300">
                    Get more job opportunities and higher pay
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={() => router.push("/profile/complete?role=day_laborer")}
                    className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    Complete Profile
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
