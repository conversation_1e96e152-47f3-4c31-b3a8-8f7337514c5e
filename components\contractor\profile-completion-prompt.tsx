'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  AlertCircle, 
  CheckCircle, 
  Star, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Shield,
  Clock,
  Award,
  Zap,
  Target,
  ArrowRight,
  X
} from 'lucide-react'
import { useState } from 'react'

interface ProfileCompletionPromptProps {
  role: 'contractor' | 'homeowner' | 'day_laborer'
  completionPercentage: number
  onComplete: () => void
  benefits: string[]
  onDismiss?: () => void
}

export function ProfileCompletionPrompt({
  role,
  completionPercentage,
  onComplete,
  benefits,
  onDismiss
}: ProfileCompletionPromptProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (isDismissed) return null

  const getRoleColor = () => {
    switch (role) {
      case 'contractor':
        return 'from-emerald-500 to-teal-600'
      case 'homeowner':
        return 'from-blue-500 to-indigo-600'
      case 'day_laborer':
        return 'from-orange-500 to-red-600'
      default:
        return 'from-slate-500 to-slate-600'
    }
  }

  const getRoleIcon = () => {
    switch (role) {
      case 'contractor':
        return <Award className="h-6 w-6" />
      case 'homeowner':
        return <Target className="h-6 w-6" />
      case 'day_laborer':
        return <Zap className="h-6 w-6" />
      default:
        return <Star className="h-6 w-6" />
    }
  }

  const getRoleTitle = () => {
    switch (role) {
      case 'contractor':
        return 'Contractor'
      case 'homeowner':
        return 'Homeowner'
      case 'day_laborer':
        return 'Day Laborer'
      default:
        return 'Profile'
    }
  }

  const getCompletionMessage = () => {
    if (completionPercentage === 0) {
      return `Complete your ${getRoleTitle().toLowerCase()} profile to unlock all features`
    } else if (completionPercentage < 50) {
      return `You're ${completionPercentage}% done! Continue building your profile`
    } else if (completionPercentage < 100) {
      return `Almost there! ${100 - completionPercentage}% left to complete your profile`
    } else {
      return `Your ${getRoleTitle().toLowerCase()} profile is complete!`
    }
  }

  const getUrgencyLevel = () => {
    if (completionPercentage < 30) return 'high'
    if (completionPercentage < 70) return 'medium'
    return 'low'
  }

  const urgencyLevel = getUrgencyLevel()

  return (
    <Card className={`mb-8 border-0 shadow-xl ${
      urgencyLevel === 'high' 
        ? 'bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20' 
        : urgencyLevel === 'medium'
        ? 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20'
        : 'bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20'
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl bg-gradient-to-r ${getRoleColor()} text-white shadow-lg`}>
              {getRoleIcon()}
            </div>
            <div>
              <CardTitle className="text-xl text-slate-900 dark:text-white">
                {getCompletionMessage()}
              </CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-300 mt-1">
                {completionPercentage < 100 
                  ? `Unlock premium features and increase your visibility`
                  : `You have access to all premium features!`
                }
              </CardDescription>
            </div>
          </div>
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDismissed(true)}
              className="text-slate-500 hover:text-slate-700"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium text-slate-700 dark:text-slate-300">
              Profile Completion
            </span>
            <span className={`font-bold ${
              completionPercentage < 50 ? 'text-red-600' : 
              completionPercentage < 100 ? 'text-yellow-600' : 'text-emerald-600'
            }`}>
              {completionPercentage}%
            </span>
          </div>
          <Progress 
            value={completionPercentage} 
            className={`h-3 ${
              completionPercentage < 50 ? '[&>div]:bg-red-500' : 
              completionPercentage < 100 ? '[&>div]:bg-yellow-500' : '[&>div]:bg-emerald-500'
            }`}
          />
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-white/60 dark:bg-slate-800/60 rounded-lg">
              <div className={`p-2 rounded-lg bg-gradient-to-r ${getRoleColor()} text-white`}>
                {index === 0 && <TrendingUp className="h-4 w-4" />}
                {index === 1 && <Users className="h-4 w-4" />}
                {index === 2 && <DollarSign className="h-4 w-4" />}
                {index === 3 && <Shield className="h-4 w-4" />}
                {index >= 4 && <Star className="h-4 w-4" />}
              </div>
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                {benefit}
              </span>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-3 gap-4 p-4 bg-white/60 dark:bg-slate-800/60 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">3x</div>
            <div className="text-xs text-slate-600 dark:text-slate-400">More Visibility</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">85%</div>
            <div className="text-xs text-slate-600 dark:text-slate-400">Higher Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-900 dark:text-white">2x</div>
            <div className="text-xs text-slate-600 dark:text-slate-400">More Opportunities</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={onComplete}
            className={`flex-1 bg-gradient-to-r ${getRoleColor()} hover:shadow-lg transition-all duration-300`}
          >
            {completionPercentage === 0 ? 'Start Profile Setup' : 'Continue Setup'}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
          {completionPercentage > 0 && (
            <Button
              variant="outline"
              onClick={() => setIsDismissed(true)}
              className="sm:w-auto"
            >
              Maybe Later
            </Button>
          )}
        </div>

        {/* Urgency Indicator */}
        {urgencyLevel === 'high' && (
          <div className="flex items-center gap-2 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">
              Complete your profile to access premium job listings and features
            </span>
          </div>
        )}

        {completionPercentage === 100 && (
          <div className="flex items-center gap-2 p-3 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200 rounded-lg">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">
              Congratulations! Your profile is complete and you have access to all features.
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
