import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { ReduxProvider } from "@/components/providers/redux-provider";
import { AuthProvider } from "@/components/providers/auth-provider";
import { AuthModal } from "@/components/auth/auth-modal";
import { OnboardingModal } from "@/components/auth/onboarding-modal";
import { Notifications } from "@/components/ui/notifications";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "BuildPro - Connect with Home Service Professionals",
  description:
    "Find skilled contractors and day laborers for all your home improvement needs",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="antialiased" suppressHydrationWarning>
      <body className={inter.className}>
        <ReduxProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AuthProvider>
              <div className="min-h-screen bg-background">
                <Header />
                <main>{children}</main>
                <Footer />
                <AuthModal />
                <OnboardingModal />
                <Notifications />
              </div>
            </AuthProvider>
          </ThemeProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
