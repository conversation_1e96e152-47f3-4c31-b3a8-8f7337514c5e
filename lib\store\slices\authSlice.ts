import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { User } from "@supabase/supabase-js";
import { Profile, UserRole } from "@/lib/supabase/types";

interface ProfileCompletionStatus {
  completed: boolean;
  percentage: number;
  lastUpdated: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  userRoles: UserRole[];
  selectedRoles: UserRole[] | null;
  activeRole: UserRole | null;
  profileCompletionStatus: Record<UserRole, ProfileCompletionStatus> | null;
  roleSelectionModalOpen: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
  emailVerificationSent: boolean;
  onboardingStep:
    | "email-verification"
    | "general-dashboard"
    | "role-selection"
    | "profile-completion"
    | "completed";
}

const initialState: AuthState = {
  user: null,
  profile: null,
  userRoles: [],
  selectedRoles: null,
  activeRole: null,
  profileCompletionStatus: null,
  roleSelectionModalOpen: false,
  isLoading: true,
  isAuthenticated: false,
  emailVerificationSent: false,
  onboardingStep: "email-verification",
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;
    },
    setProfile: (state, action: PayloadAction<Profile | null>) => {
      state.profile = action.payload;
      if (action.payload) {
        // Determine onboarding step based on profile data and current user roles
        console.log(
          "Setting profile, email_verified:",
          action.payload.email_verified,
          "userRoles:",
          state.userRoles.length
        );

        if (!action.payload.email_verified) {
          state.onboardingStep = "email-verification";
        } else if (state.userRoles.length === 0) {
          // After email verification, show general dashboard first
          state.onboardingStep = "general-dashboard";
        } else if (!action.payload.onboarding_completed) {
          state.onboardingStep = "profile-completion";
        } else {
          state.onboardingStep = "completed";
        }

        console.log("Onboarding step set to:", state.onboardingStep);
      }
    },
    setUserRoles: (state, action: PayloadAction<UserRole[]>) => {
      state.userRoles = action.payload;

      // Also update selectedRoles if not already set
      if (!state.selectedRoles && action.payload.length > 0) {
        state.selectedRoles = action.payload;
        // Set first role as active if no active role is set
        if (!state.activeRole) {
          state.activeRole = action.payload[0];
        }
      }

      console.log(
        "Setting user roles:",
        action.payload.length,
        "profile email_verified:",
        state.profile?.email_verified
      );

      // Update onboarding step if needed
      if (state.profile && state.profile.email_verified) {
        if (action.payload.length === 0) {
          // If no roles selected, stay on general dashboard
          state.onboardingStep = "general-dashboard";
        } else if (!state.profile.onboarding_completed) {
          state.onboardingStep = "profile-completion";
        } else {
          state.onboardingStep = "completed";
        }
        console.log("Onboarding step updated to:", state.onboardingStep);
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setEmailVerificationSent: (state, action: PayloadAction<boolean>) => {
      state.emailVerificationSent = action.payload;
    },
    setOnboardingStep: (
      state,
      action: PayloadAction<AuthState["onboardingStep"]>
    ) => {
      state.onboardingStep = action.payload;
    },
    setSelectedRoles: (state, action: PayloadAction<UserRole[]>) => {
      state.selectedRoles = action.payload;
      // Set first role as active if no active role is set
      if (action.payload.length > 0 && !state.activeRole) {
        state.activeRole = action.payload[0];
      }

      // Initialize profile completion status for new roles
      if (!state.profileCompletionStatus) {
        state.profileCompletionStatus = {} as Record<
          UserRole,
          ProfileCompletionStatus
        >;
      }

      action.payload.forEach((role) => {
        if (!state.profileCompletionStatus![role]) {
          state.profileCompletionStatus![role] = {
            completed: false,
            percentage: 0,
            lastUpdated: new Date().toISOString(),
          };
        }
      });
    },
    setActiveRole: (state, action: PayloadAction<UserRole>) => {
      state.activeRole = action.payload;
    },
    setProfileCompletionStatus: (
      state,
      action: PayloadAction<Record<UserRole, ProfileCompletionStatus>>
    ) => {
      state.profileCompletionStatus = action.payload;
    },
    openRoleSelectionModal: (state) => {
      state.roleSelectionModalOpen = true;
    },
    closeRoleSelectionModal: (state) => {
      state.roleSelectionModalOpen = false;
    },
    logout: (state) => {
      state.user = null;
      state.profile = null;
      state.userRoles = [];
      state.selectedRoles = null;
      state.activeRole = null;
      state.profileCompletionStatus = null;
      state.roleSelectionModalOpen = false;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.emailVerificationSent = false;
      state.onboardingStep = "email-verification";
    },
  },
});

export const {
  setUser,
  setProfile,
  setUserRoles,
  setSelectedRoles,
  setActiveRole,
  setProfileCompletionStatus,
  openRoleSelectionModal,
  closeRoleSelectionModal,
  setLoading,
  setEmailVerificationSent,
  setOnboardingStep,
  logout,
} = authSlice.actions;

export default authSlice.reducer;
