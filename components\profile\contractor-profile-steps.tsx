"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ProgressIndicator } from "@/components/ui/progress-indicator";
import {
  ArrowLeft,
  ArrowRight,
  Building,
  Hammer,
  Award,
  Upload,
} from "lucide-react";

// Step 1: Business Information Schema
const businessInfoSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  business_registration: z.string().optional(),
  license_number: z.string().optional(),
  insurance_provider: z.string().optional(),
  insurance_policy_number: z.string().optional(),
  years_experience: z.enum(["1-2", "3-5", "6-10", "11-15", "16-20", "20+"]),
  business_description: z
    .string()
    .min(50, "Please provide at least 50 characters"),
});

// Step 2: Services and Skills Schema
const servicesSkillsSchema = z.object({
  service_categories: z
    .array(z.string())
    .min(1, "Select at least one service category"),
  specializations: z.array(z.string()),
  service_areas: z.array(z.string()).min(1, "Select at least one service area"),
  hourly_rate_min: z.number().min(10, "Minimum rate must be at least $10"),
  hourly_rate_max: z.number().min(10, "Maximum rate must be at least $10"),
  project_size_preference: z.enum(["small", "medium", "large", "all"]),
});

// Step 3: Portfolio and Credentials Schema
const portfolioCredentialsSchema = z.object({
  portfolio_images: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  availability_status: z.enum(["available", "busy", "unavailable"]),
  response_time: z.enum(["within_hour", "within_day", "within_week"]),
  travel_radius: z.enum(["10", "25", "50", "100", "unlimited"]),
});

type BusinessInfoData = z.infer<typeof businessInfoSchema>;
type ServicesSkillsData = z.infer<typeof servicesSkillsSchema>;
type PortfolioCredentialsData = z.infer<typeof portfolioCredentialsSchema>;

interface ContractorProfileStepsProps {
  onComplete: (
    data: BusinessInfoData & ServicesSkillsData & PortfolioCredentialsData
  ) => void;
  onBack?: () => void;
  initialData?: Partial<
    BusinessInfoData & ServicesSkillsData & PortfolioCredentialsData
  >;
}

export function ContractorProfileSteps({
  onComplete,
  onBack,
  initialData,
}: ContractorProfileStepsProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [formData, setFormData] = useState<
    Partial<BusinessInfoData & ServicesSkillsData & PortfolioCredentialsData>
  >(initialData || {});

  const steps = [
    {
      id: "business-info",
      title: "Business Information",
      description: "Company details and credentials",
    },
    {
      id: "services-skills",
      title: "Services & Skills",
      description: "What services you offer and where",
    },
    {
      id: "portfolio-credentials",
      title: "Portfolio & Credentials",
      description: "Showcase your work and availability",
    },
  ];

  const serviceCategories = [
    "Plumbing",
    "Electrical",
    "HVAC",
    "Roofing",
    "Flooring",
    "Painting",
    "Landscaping",
    "Cleaning",
    "Handyman",
    "Kitchen Renovation",
    "Bathroom Renovation",
    "General Contracting",
    "Carpentry",
    "Masonry",
    "Drywall",
    "Insulation",
    "Windows & Doors",
    "Fencing",
    "Concrete",
  ];

  const specializations = [
    "Residential",
    "Commercial",
    "Emergency Services",
    "Green/Eco-Friendly",
    "Historic Restoration",
    "Luxury Projects",
    "Budget-Friendly",
    "Quick Turnaround",
  ];

  const serviceAreas = [
    "Downtown",
    "North Side",
    "South Side",
    "East Side",
    "West Side",
    "Suburbs",
    "Rural Areas",
    "Entire Metro Area",
    "Surrounding Counties",
  ];

  // Step 1: Business Information Form
  const BusinessInfoStep = () => {
    const form = useForm<BusinessInfoData>({
      resolver: zodResolver(businessInfoSchema),
      defaultValues: {
        business_name: formData.business_name || "",
        business_registration: formData.business_registration || "",
        license_number: formData.license_number || "",
        insurance_provider: formData.insurance_provider || "",
        insurance_policy_number: formData.insurance_policy_number || "",
        years_experience: formData.years_experience || "3-5",
        business_description: formData.business_description || "",
      },
    });

    const onSubmit = (data: BusinessInfoData) => {
      setFormData((prev) => ({ ...prev, ...data }));
      setCompletedSteps((prev) => [...prev.filter((s) => s !== 1), 1]);
      setCurrentStep(2);
    };

    return (
      <div className="space-y-8">
        {/* Step Header */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 text-emerald-800 dark:text-emerald-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Building className="h-4 w-4" />
            Step 1 of 3
          </div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Business Information
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            Tell us about your business to build trust with potential clients
          </p>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-emerald-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Building className="h-6 w-6" />
                </div>
                Business Details
              </CardTitle>
              <CardDescription className="text-emerald-100">
                Tell us about your business to build trust with potential
                clients
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="business_name">Business Name *</Label>
                <Input
                  id="business_name"
                  placeholder="Your Company Name"
                  {...form.register("business_name")}
                />
                {form.formState.errors.business_name && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.business_name.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="business_registration">
                    Business Registration #
                  </Label>
                  <Input
                    id="business_registration"
                    placeholder="Optional"
                    {...form.register("business_registration")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="license_number">License Number</Label>
                  <Input
                    id="license_number"
                    placeholder="Optional"
                    {...form.register("license_number")}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="insurance_provider">Insurance Provider</Label>
                  <Input
                    id="insurance_provider"
                    placeholder="e.g., State Farm, Allstate"
                    {...form.register("insurance_provider")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="insurance_policy_number">Policy Number</Label>
                  <Input
                    id="insurance_policy_number"
                    placeholder="Optional"
                    {...form.register("insurance_policy_number")}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="years_experience">Years of Experience</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("years_experience", value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-2">1-2 years</SelectItem>
                    <SelectItem value="3-5">3-5 years</SelectItem>
                    <SelectItem value="6-10">6-10 years</SelectItem>
                    <SelectItem value="11-15">11-15 years</SelectItem>
                    <SelectItem value="16-20">16-20 years</SelectItem>
                    <SelectItem value="20+">20+ years</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="business_description">
                  Business Description *
                </Label>
                <Textarea
                  id="business_description"
                  placeholder="Describe your business, what makes you unique, and your approach to projects..."
                  rows={4}
                  {...form.register("business_description")}
                />
                {form.formState.errors.business_description && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.business_description.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="min-w-[120px] border-slate-300 hover:bg-slate-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="min-w-[160px] bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Continue to Services
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </form>
      </div>
    );
  };

  // Step 2: Services and Skills Form
  const ServicesSkillsStep = () => {
    const form = useForm<ServicesSkillsData>({
      resolver: zodResolver(servicesSkillsSchema),
      defaultValues: {
        service_categories: formData.service_categories || [],
        specializations: formData.specializations || [],
        service_areas: formData.service_areas || [],
        hourly_rate_min: formData.hourly_rate_min || 50,
        hourly_rate_max: formData.hourly_rate_max || 100,
        project_size_preference: formData.project_size_preference || "all",
      },
    });

    const onSubmit = (data: ServicesSkillsData) => {
      setFormData((prev) => ({ ...prev, ...data }));
      setCompletedSteps((prev) => [...prev.filter((s) => s !== 2), 2]);
      setCurrentStep(3);
    };

    return (
      <div className="space-y-8">
        {/* Step Header */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-800 dark:text-orange-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Hammer className="h-4 w-4" />
            Step 2 of 3
          </div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Services & Skills
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            Select the services you provide and your specializations
          </p>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-orange-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Hammer className="h-6 w-6" />
                </div>
                Services Offered
              </CardTitle>
              <CardDescription className="text-orange-100">
                Select the services you provide and your specializations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Service Categories *</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {serviceCategories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={category}
                        checked={form
                          .watch("service_categories")
                          ?.includes(category)}
                        onCheckedChange={(checked) => {
                          const current =
                            form.getValues("service_categories") || [];
                          if (checked) {
                            form.setValue("service_categories", [
                              ...current,
                              category,
                            ]);
                          } else {
                            form.setValue(
                              "service_categories",
                              current.filter((c) => c !== category)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={category} className="text-sm">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
                {form.formState.errors.service_categories && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.service_categories.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Specializations</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {specializations.map((spec) => (
                    <div key={spec} className="flex items-center space-x-2">
                      <Checkbox
                        id={spec}
                        checked={form.watch("specializations")?.includes(spec)}
                        onCheckedChange={(checked) => {
                          const current =
                            form.getValues("specializations") || [];
                          if (checked) {
                            form.setValue("specializations", [
                              ...current,
                              spec,
                            ]);
                          } else {
                            form.setValue(
                              "specializations",
                              current.filter((s) => s !== spec)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={spec} className="text-sm">
                        {spec}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Service Areas *</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {serviceAreas.map((area) => (
                    <div key={area} className="flex items-center space-x-2">
                      <Checkbox
                        id={area}
                        checked={form.watch("service_areas")?.includes(area)}
                        onCheckedChange={(checked) => {
                          const current = form.getValues("service_areas") || [];
                          if (checked) {
                            form.setValue("service_areas", [...current, area]);
                          } else {
                            form.setValue(
                              "service_areas",
                              current.filter((a) => a !== area)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={area} className="text-sm">
                        {area}
                      </Label>
                    </div>
                  ))}
                </div>
                {form.formState.errors.service_areas && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.service_areas.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hourly_rate_min">
                    Minimum Hourly Rate ($)
                  </Label>
                  <Input
                    id="hourly_rate_min"
                    type="number"
                    min="10"
                    {...form.register("hourly_rate_min", {
                      valueAsNumber: true,
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hourly_rate_max">
                    Maximum Hourly Rate ($)
                  </Label>
                  <Input
                    id="hourly_rate_max"
                    type="number"
                    min="10"
                    {...form.register("hourly_rate_max", {
                      valueAsNumber: true,
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_size_preference">
                    Project Size Preference
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("project_size_preference", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">
                        Small Projects (Under $5K)
                      </SelectItem>
                      <SelectItem value="medium">
                        Medium Projects ($5K-$25K)
                      </SelectItem>
                      <SelectItem value="large">
                        Large Projects ($25K+)
                      </SelectItem>
                      <SelectItem value="all">All Project Sizes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setCurrentStep(1)}
              className="min-w-[120px] border-slate-300 hover:bg-slate-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              type="submit"
              className="min-w-[160px] bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Continue to Portfolio
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </form>
      </div>
    );
  };

  // Step 3: Portfolio and Credentials Form
  const PortfolioCredentialsStep = () => {
    const form = useForm<PortfolioCredentialsData>({
      resolver: zodResolver(portfolioCredentialsSchema),
      defaultValues: {
        portfolio_images: formData.portfolio_images || [],
        certifications: formData.certifications || [],
        availability_status: formData.availability_status || "available",
        response_time: formData.response_time || "within_day",
        travel_radius: formData.travel_radius || "25",
      },
    });

    const onSubmit = (data: PortfolioCredentialsData) => {
      const finalData = { ...formData, ...data } as BusinessInfoData &
        ServicesSkillsData &
        PortfolioCredentialsData;
      setCompletedSteps((prev) => [...prev.filter((s) => s !== 3), 3]);
      onComplete(finalData);
    };

    return (
      <div className="space-y-8">
        {/* Step Header */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-800 dark:text-purple-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Award className="h-4 w-4" />
            Step 3 of 3
          </div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Portfolio & Credentials
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            Showcase your work and set your availability preferences
          </p>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-purple-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Award className="h-6 w-6" />
                </div>
                Portfolio & Credentials
              </CardTitle>
              <CardDescription className="text-purple-100">
                Showcase your work and set your availability preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Portfolio Images</Label>
                <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-slate-400" />
                  <p className="text-sm text-muted-foreground mb-2">
                    Upload photos of your best work (Coming Soon)
                  </p>
                  <Button type="button" variant="outline" disabled>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Images
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="certifications">Certifications</Label>
                <Textarea
                  id="certifications"
                  placeholder="List your certifications, licenses, and professional qualifications..."
                  rows={3}
                  onChange={(e) => {
                    const certs = e.target.value
                      .split("\n")
                      .filter((cert) => cert.trim());
                    form.setValue("certifications", certs);
                  }}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="availability_status">
                    Current Availability
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("availability_status", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select availability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">
                        Available for New Projects
                      </SelectItem>
                      <SelectItem value="busy">
                        Busy - Limited Availability
                      </SelectItem>
                      <SelectItem value="unavailable">
                        Not Taking New Projects
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="response_time">Response Time</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("response_time", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select response time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="within_hour">Within 1 Hour</SelectItem>
                      <SelectItem value="within_day">
                        Within 24 Hours
                      </SelectItem>
                      <SelectItem value="within_week">Within 1 Week</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="travel_radius">Travel Radius</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("travel_radius", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select travel radius" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 miles</SelectItem>
                      <SelectItem value="25">25 miles</SelectItem>
                      <SelectItem value="50">50 miles</SelectItem>
                      <SelectItem value="100">100 miles</SelectItem>
                      <SelectItem value="unlimited">Unlimited</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setCurrentStep(2)}
              className="min-w-[120px] border-slate-300 hover:bg-slate-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              type="submit"
              className="min-w-[180px] bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Complete Profile ✨
            </Button>
          </div>
        </form>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {currentStep === 1 && <BusinessInfoStep />}
      {currentStep === 2 && <ServicesSkillsStep />}
      {currentStep === 3 && <PortfolioCredentialsStep />}
    </div>
  );
}
