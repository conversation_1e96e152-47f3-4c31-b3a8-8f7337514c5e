"use client";

import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import { setOnboardingStep } from "@/lib/store/slices/authSlice";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HomeownerProfileSteps } from "./homeowner-profile-steps";
import { ContractorProfileSteps } from "./contractor-profile-steps";
// import { DayLaborerProfileSteps } from "./day-laborer-profile-steps";
import { UserRole } from "@/lib/supabase/types";
import {
  Home,
  Briefcase,
  Wrench,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
} from "lucide-react";
import React from "react";

interface ProfileCompletionProps {
  onComplete?: () => void;
}

export function ProfileCompletion({ onComplete }: ProfileCompletionProps) {
  const dispatch = useDispatch();
  const { userRoles } = useSelector((state: RootState) => state.auth);
  const [currentRoleIndex, setCurrentRoleIndex] = useState(0);
  const [completedRoles, setCompletedRoles] = useState<Set<UserRole>>(
    new Set()
  );

  const roleConfig = {
    homeowner: {
      icon: <Home className="h-6 w-6" />,
      title: "Homeowner Profile",
      description: "Complete your property information",
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    contractor: {
      icon: <Briefcase className="h-6 w-6" />,
      title: "Contractor Profile",
      description: "Set up your business information",
      color: "text-emerald-600 dark:text-emerald-400",
      bgColor: "bg-emerald-100 dark:bg-emerald-900/20",
    },
    day_laborer: {
      icon: <Wrench className="h-6 w-6" />,
      title: "Day Laborer Profile",
      description: "Showcase your skills and availability",
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900/20",
    },
  };

  const currentRole = userRoles[currentRoleIndex];
  const currentConfig = roleConfig[currentRole];

  const handleRoleComplete = (role: UserRole) => {
    const newCompleted = new Set(completedRoles);
    newCompleted.add(role);
    setCompletedRoles(newCompleted);

    // Move to next role or complete onboarding
    if (currentRoleIndex < userRoles.length - 1) {
      setCurrentRoleIndex(currentRoleIndex + 1);
    } else {
      // All roles completed
      dispatch(setOnboardingStep("completed"));
      onComplete?.();
    }
  };

  const handleSkipRole = () => {
    if (currentRoleIndex < userRoles.length - 1) {
      setCurrentRoleIndex(currentRoleIndex + 1);
    } else {
      // Skip remaining and complete onboarding
      dispatch(setOnboardingStep("completed"));
      onComplete?.();
    }
  };

  const handlePreviousRole = () => {
    if (currentRoleIndex > 0) {
      setCurrentRoleIndex(currentRoleIndex - 1);
    }
  };

  const renderProfileForm = () => {
    switch (currentRole) {
      case "homeowner":
        return (
          <HomeownerProfileSteps
            onComplete={() => handleRoleComplete("homeowner")}
            onBack={handlePreviousRole}
          />
        );
      case "contractor":
        return (
          <ContractorProfileSteps
            onComplete={() => handleRoleComplete("contractor")}
            onBack={handlePreviousRole}
          />
        );
      case "day_laborer":
        return (
          <div className="text-center p-8">
            <p>Day Laborer profile completion coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  if (userRoles.length === 0) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle>No Roles Selected</CardTitle>
          <CardDescription>
            Please select at least one role to continue with profile setup.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => dispatch(setOnboardingStep("role-selection"))}
            className="w-full"
          >
            Select Roles
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          Complete Your Profiles
        </h2>
        <p className="text-muted-foreground">
          Set up your profile for each role you selected
        </p>

        {/* Role Progress */}
        <div className="flex justify-center items-center gap-4">
          {userRoles.map((role, index) => {
            const config = roleConfig[role];
            const isActive = index === currentRoleIndex;
            const isCompleted = completedRoles.has(role);

            return (
              <div
                key={role}
                className={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium transition-all ${
                  isActive
                    ? `${config.bgColor} ${config.color} ring-2 ring-offset-2 ring-current`
                    : isCompleted
                    ? "bg-emerald-100 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-400"
                    : "bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400"
                }`}
              >
                {isCompleted ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <div
                    className={`w-4 h-4 ${config.bgColor} rounded-full flex items-center justify-center`}
                  >
                    {React.cloneElement(config.icon, { className: "h-3 w-3" })}
                  </div>
                )}
                <span className="capitalize">{role.replace("_", " ")}</span>
              </div>
            );
          })}
        </div>

        {/* Current Role Header */}
        <div className="flex items-center justify-center gap-3">
          <div
            className={`w-8 h-8 ${currentConfig.bgColor} rounded-full flex items-center justify-center`}
          >
            {React.cloneElement(currentConfig.icon, {
              className: `h-5 w-5 ${currentConfig.color}`,
            })}
          </div>
          <div className="text-left">
            <h3 className="font-semibold text-lg">{currentConfig.title}</h3>
            <p className="text-sm text-muted-foreground">
              {currentConfig.description}
            </p>
          </div>
        </div>
      </div>

      {/* Profile Form */}
      <div className="max-w-2xl mx-auto">{renderProfileForm()}</div>

      {/* Navigation */}
      {userRoles.length > 1 && (
        <div className="flex justify-between items-center max-w-2xl mx-auto">
          <Button
            variant="outline"
            onClick={handlePreviousRole}
            disabled={currentRoleIndex === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <span className="text-sm text-muted-foreground">
            {currentRoleIndex + 1} of {userRoles.length}
          </span>

          <Button
            variant="outline"
            onClick={handleSkipRole}
            className="flex items-center gap-2"
          >
            Skip
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Skip All Option */}
      <div className="text-center">
        <Button
          variant="ghost"
          onClick={() => {
            dispatch(setOnboardingStep("completed"));
            onComplete?.();
          }}
          className="text-muted-foreground hover:text-foreground"
        >
          Skip all profiles for now
        </Button>
      </div>
    </div>
  );
}
