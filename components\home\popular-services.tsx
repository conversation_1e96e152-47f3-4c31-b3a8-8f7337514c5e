import { Heart } from "lucide-react";
import { Service } from "@/lib/types";
import { popularServices } from "@/lib/data";
import Link from "next/link";

export function PopularServices() {
  return (
    <section className="section-padding bg-background">
      <div className="container">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12 lg:mb-16">
          <div className="max-w-2xl">
            <h2 className="heading-2 mb-4">Popular Services</h2>
            <p className="body-medium text-muted-foreground">
              Discover top-rated services by our trusted professionals
            </p>
          </div>
          <div className="mt-6 md:mt-0">
            <div className="flex flex-wrap gap-2">
              <button className="text-sm font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 px-4 py-2 rounded-full hover:bg-orange-200 dark:hover:bg-orange-800/40 transition-colors">
                Newest
              </button>
              <button className="text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-slate-100 dark:hover:bg-slate-800 px-4 py-2 rounded-full transition-colors">
                Top Rated
              </button>
              <button className="text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-slate-100 dark:hover:bg-slate-800 px-4 py-2 rounded-full transition-colors">
                Most Popular
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {popularServices.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>

        <div className="flex justify-center mt-12 lg:mt-16">
          <Link href="/services">
            <button className="px-8 py-3 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-xl font-medium text-base transition-all duration-200 hover:shadow-md">
              View All Services
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}

function ServiceCard({ service }: { service: Service }) {
  return (
    <div className="group rounded-2xl border bg-white dark:bg-slate-800 overflow-hidden card-hover">
      <div className="relative">
        <div className="relative h-48 w-full overflow-hidden">
          <img
            src={service.images[0]}
            alt={service.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          {service.featured && (
            <div className="absolute top-3 left-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg">
              Featured
            </div>
          )}
          <button className="absolute top-3 right-3 bg-white/90 dark:bg-gray-900/90 p-2 rounded-full backdrop-blur-sm shadow-lg hover:bg-white dark:hover:bg-gray-900 transition-all duration-200 hover:scale-110">
            <Heart className="h-4 w-4 text-muted-foreground hover:text-rose-500 transition-colors" />
          </button>
        </div>
      </div>

      <div className="p-6">
        <div className="flex items-center space-x-3 mb-3">
          <img
            src={service.providerAvatar || "https://via.placeholder.com/40"}
            alt={service.providerName}
            className="w-9 h-9 rounded-full object-cover border-2 border-slate-200 dark:border-slate-700"
          />
          <span className="text-sm font-semibold text-slate-700 dark:text-slate-300">
            {service.providerName}
          </span>
        </div>

        <h3 className="font-semibold text-base line-clamp-2 min-h-[48px] mb-3 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
          <Link href={`/services/${service.id}`}>{service.title}</Link>
        </h3>

        <div className="flex items-center mb-4">
          <div className="flex items-center text-amber-500">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className={`w-4 h-4 ${
                  star <= Math.floor(service.providerRating || 0)
                    ? "fill-current"
                    : "text-muted stroke-current"
                }`}
                viewBox="0 0 24 24"
              >
                <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
              </svg>
            ))}
            <span className="ml-2 text-sm font-medium text-slate-700 dark:text-slate-300">
              {service.providerRating} ({service.completedJobs})
            </span>
          </div>
        </div>

        <div className="border-t border-slate-200 dark:border-slate-700 pt-4 mt-4 flex items-center justify-between">
          <div>
            <span className="text-lg font-bold text-slate-900 dark:text-white">
              ${service.price}
            </span>
            {service.priceType !== "fixed" && (
              <span className="text-sm text-muted-foreground">
                /{service.priceType}
              </span>
            )}
          </div>
          <div className="flex items-center text-sm text-muted-foreground bg-slate-100 dark:bg-slate-700 px-3 py-1 rounded-full">
            <span>
              {service.deliveryTime} {service.deliveryTimeUnit}
              {service.deliveryTime > 1 ? "s" : ""}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
