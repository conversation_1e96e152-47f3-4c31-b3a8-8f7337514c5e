import { createApi, fakeBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "@/lib/supabase/client";
import {
  Profile,
  UserRole,
  UserRoleRecord,
  HomeownerProfile,
  ContractorProfile,
  DayLaborerProfile,
} from "@/lib/supabase/types";

export interface UpdateProfileData {
  full_name?: string;
  phone?: string;
  avatar_url?: string;
}

export interface CreateUserRoleData {
  role: UserRole;
}

export interface UpdateHomeownerProfileData {
  property_type?: string;
  property_size?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  profile_completed?: boolean;
}

export interface UpdateContractorProfileData {
  business_name?: string;
  license_number?: string;
  insurance_verified?: boolean;
  specializations?: string[];
  years_experience?: number;
  service_radius?: number;
  hourly_rate?: number;
  profile_completed?: boolean;
}

export interface UpdateDayLaborerProfileData {
  skills?: string[];
  availability?: any;
  hourly_rate?: number;
  transportation?: boolean;
  tools_owned?: string[];
  profile_completed?: boolean;
}

export const profileApi = createApi({
  reducerPath: "profileApi",
  baseQuery: fakeBaseQuery(),
  tagTypes: [
    "Profile",
    "UserRoles",
    "HomeownerProfile",
    "ContractorProfile",
    "DayLaborerProfile",
  ],
  endpoints: (builder) => ({
    getProfile: builder.query<Profile, void>({
      queryFn: async () => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      providesTags: ["Profile"],
    }),

    updateProfile: builder.mutation<Profile, UpdateProfileData>({
      queryFn: async (updateData) => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("profiles")
            .update(updateData)
            .eq("id", user.id)
            .select()
            .single();

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Profile"],
    }),

    getUserRoles: builder.query<UserRoleRecord[], void>({
      queryFn: async () => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("user_roles")
            .select("*")
            .eq("user_id", user.id)
            .eq("is_active", true);

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: data || [] };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      providesTags: ["UserRoles"],
    }),

    createUserRole: builder.mutation<UserRoleRecord, CreateUserRoleData>({
      queryFn: async ({ role }) => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("user_roles")
            .insert({
              user_id: user.id,
              role,
            })
            .select()
            .single();

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["UserRoles"],
    }),

    getHomeownerProfile: builder.query<HomeownerProfile, void>({
      queryFn: async () => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("homeowner_profiles")
            .select("*")
            .eq("user_id", user.id)
            .single();

          if (error && error.code !== "PGRST116") {
            return { error: { error: error.message } };
          }

          return { data: data || null };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      providesTags: ["HomeownerProfile"],
    }),

    updateHomeownerProfile: builder.mutation<
      HomeownerProfile,
      UpdateHomeownerProfileData
    >({
      queryFn: async (updateData) => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          // First try to update existing profile
          const { data: existingProfile } = await supabase
            .from("homeowner_profiles")
            .select("id")
            .eq("user_id", user.id)
            .single();

          let data, error;

          if (existingProfile) {
            // Update existing profile
            const result = await supabase
              .from("homeowner_profiles")
              .update(updateData)
              .eq("user_id", user.id)
              .select()
              .single();

            data = result.data;
            error = result.error;
          } else {
            // Create new profile
            const result = await supabase
              .from("homeowner_profiles")
              .insert({
                user_id: user.id,
                ...updateData,
              })
              .select()
              .single();

            data = result.data;
            error = result.error;
          }

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["HomeownerProfile"],
    }),

    getContractorProfile: builder.query<ContractorProfile, void>({
      queryFn: async () => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("contractor_profiles")
            .select("*")
            .eq("user_id", user.id)
            .single();

          if (error && error.code !== "PGRST116") {
            return { error: { error: error.message } };
          }

          return { data: data || null };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      providesTags: ["ContractorProfile"],
    }),

    updateContractorProfile: builder.mutation<
      ContractorProfile,
      UpdateContractorProfileData
    >({
      queryFn: async (updateData) => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          // First try to update existing profile
          const { data: existingProfile } = await supabase
            .from("contractor_profiles")
            .select("id")
            .eq("user_id", user.id)
            .single();

          let data, error;

          if (existingProfile) {
            // Update existing profile
            const result = await supabase
              .from("contractor_profiles")
              .update(updateData)
              .eq("user_id", user.id)
              .select()
              .single();

            data = result.data;
            error = result.error;
          } else {
            // Create new profile
            const result = await supabase
              .from("contractor_profiles")
              .insert({
                user_id: user.id,
                ...updateData,
              })
              .select()
              .single();

            data = result.data;
            error = result.error;
          }

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["ContractorProfile"],
    }),

    getDayLaborerProfile: builder.query<DayLaborerProfile, void>({
      queryFn: async () => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          const { data, error } = await supabase
            .from("day_laborer_profiles")
            .select("*")
            .eq("user_id", user.id)
            .single();

          if (error && error.code !== "PGRST116") {
            return { error: { error: error.message } };
          }

          return { data: data || null };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      providesTags: ["DayLaborerProfile"],
    }),

    updateDayLaborerProfile: builder.mutation<
      DayLaborerProfile,
      UpdateDayLaborerProfileData
    >({
      queryFn: async (updateData) => {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();

          if (!user) {
            return { error: { error: "User not authenticated" } };
          }

          // First try to update existing profile
          const { data: existingProfile } = await supabase
            .from("day_laborer_profiles")
            .select("id")
            .eq("user_id", user.id)
            .single();

          let data, error;

          if (existingProfile) {
            // Update existing profile
            const result = await supabase
              .from("day_laborer_profiles")
              .update(updateData)
              .eq("user_id", user.id)
              .select()
              .single();

            data = result.data;
            error = result.error;
          } else {
            // Create new profile
            const result = await supabase
              .from("day_laborer_profiles")
              .insert({
                user_id: user.id,
                ...updateData,
              })
              .select()
              .single();

            data = result.data;
            error = result.error;
          }

          if (error) {
            return { error: { error: error.message } };
          }

          return { data };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["DayLaborerProfile"],
    }),
  }),
});

export const {
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetUserRolesQuery,
  useCreateUserRoleMutation,
  useGetHomeownerProfileQuery,
  useUpdateHomeownerProfileMutation,
  useGetContractorProfileQuery,
  useUpdateContractorProfileMutation,
  useGetDayLaborerProfileQuery,
  useUpdateDayLaborerProfileMutation,
} = profileApi;
