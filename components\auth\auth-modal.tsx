"use client";

import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/lib/store";
import {
  closeSignUpModal,
  closeSignInModal,
  openSignUpModal,
  openSignInModal,
  openForgotPasswordModal,
  closeForgotPasswordModal,
  closeResetPasswordModal,
  closeRoleSelectionModal,
} from "@/lib/store/slices/uiSlice";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { SignUpForm } from "./sign-up-form";
import { SignInForm } from "./sign-in-form";
import { ForgotPasswordForm } from "./forgot-password-form";
import { ResetPasswordForm } from "./reset-password-form";
import { EnhancedRoleSelection } from "./enhanced-role-selection";

export function AuthModal() {
  const dispatch = useDispatch();
  const {
    isSignUpModalOpen,
    isSignInModalOpen,
    isForgotPasswordModalOpen,
    isResetPasswordModalOpen,
    isRoleSelectionModalOpen,
  } = useSelector((state: RootState) => state.ui);

  const handleSignUpSuccess = () => {
    dispatch(closeSignUpModal());
  };

  const handleSignInSuccess = () => {
    dispatch(closeSignInModal());
  };

  const handleSwitchToSignIn = () => {
    dispatch(closeSignUpModal());
    dispatch(openSignInModal());
  };

  const handleSwitchToSignUp = () => {
    dispatch(closeSignInModal());
    dispatch(openSignUpModal());
  };

  const handleForgotPassword = () => {
    dispatch(closeSignInModal());
    dispatch(openForgotPasswordModal());
  };

  const handleForgotPasswordSuccess = () => {
    dispatch(closeForgotPasswordModal());
  };

  const handleBackToSignIn = () => {
    dispatch(closeForgotPasswordModal());
    dispatch(openSignInModal());
  };

  const handleResetPasswordSuccess = () => {
    dispatch(closeResetPasswordModal());
    dispatch(openSignInModal());
  };

  return (
    <>
      {/* Sign Up Modal */}
      <Dialog
        open={isSignUpModalOpen}
        onOpenChange={() => dispatch(closeSignUpModal())}
      >
        <DialogContent className="sm:max-w-md">
          <DialogTitle className="sr-only">Sign Up</DialogTitle>
          <SignUpForm
            onSuccess={handleSignUpSuccess}
            onSwitchToSignIn={handleSwitchToSignIn}
          />
        </DialogContent>
      </Dialog>

      {/* Sign In Modal */}
      <Dialog
        open={isSignInModalOpen}
        onOpenChange={() => dispatch(closeSignInModal())}
      >
        <DialogContent className="sm:max-w-md">
          <DialogTitle className="sr-only">Sign In</DialogTitle>
          <SignInForm
            onSuccess={handleSignInSuccess}
            onSwitchToSignUp={handleSwitchToSignUp}
            onForgotPassword={handleForgotPassword}
          />
        </DialogContent>
      </Dialog>

      {/* Forgot Password Modal */}
      <Dialog
        open={isForgotPasswordModalOpen}
        onOpenChange={() => dispatch(closeForgotPasswordModal())}
      >
        <DialogContent className="sm:max-w-md">
          <DialogTitle className="sr-only">Forgot Password</DialogTitle>
          <ForgotPasswordForm
            onSuccess={handleForgotPasswordSuccess}
            onBackToSignIn={handleBackToSignIn}
          />
        </DialogContent>
      </Dialog>

      {/* Reset Password Modal */}
      <Dialog
        open={isResetPasswordModalOpen}
        onOpenChange={() => dispatch(closeResetPasswordModal())}
      >
        <DialogContent className="sm:max-w-md">
          <DialogTitle className="sr-only">Reset Password</DialogTitle>
          <ResetPasswordForm onSuccess={handleResetPasswordSuccess} />
        </DialogContent>
      </Dialog>

      {/* Role Selection Modal */}
      <Dialog
        open={isRoleSelectionModalOpen}
        onOpenChange={() => dispatch(closeRoleSelectionModal())}
      >
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogTitle className="sr-only">Role Selection</DialogTitle>
          <EnhancedRoleSelection />
        </DialogContent>
      </Dialog>
    </>
  );
}
