'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/store'
import { MyJobsList } from '@/components/jobs/my-jobs-list'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export default function MyJobsPage() {
  const router = useRouter()
  const { isAuthenticated, selectedRoles, onboardingStep } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
      return
    }

    // Check if user has homeowner role
    if (!selectedRoles?.includes('homeowner')) {
      router.push('/dashboard')
      return
    }

    // Check if onboarding is complete
    if (onboardingStep !== 'completed') {
      router.push('/profile/complete')
      return
    }
  }, [isAuthenticated, selectedRoles, onboardingStep, router])

  if (!isAuthenticated || !selectedRoles?.includes('homeowner') || onboardingStep !== 'completed') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            
            <div className="text-sm text-muted-foreground">
              My Jobs
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <MyJobsList />
      </div>
    </div>
  )
}
