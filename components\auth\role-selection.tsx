"use client";

import { useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useCreateUserRoleMutation } from "@/lib/store/api/profileApi";
import { addNotification } from "@/lib/store/slices/uiSlice";
import { setSelectedRoles, setUserRoles } from "@/lib/store/slices/authSlice";
import { UserRole } from "@/lib/supabase/types";
import { Home, Briefcase, Wrench, Check, Loader2 } from "lucide-react";

interface RoleSelectionProps {
  onComplete?: () => void;
  allowMultiple?: boolean;
}

export function RoleSelection({
  onComplete,
  allowMultiple = true,
}: RoleSelectionProps) {
  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const [createUserRole] = useCreateUserRoleMutation();

  const roles = [
    {
      id: "homeowner" as UserRole,
      title: "Homeowner",
      description: "I need help with home improvement projects and repairs",
      icon: <Home className="h-8 w-8" />,
      features: [
        "Post projects and get quotes",
        "Browse and hire contractors",
        "Manage project timelines",
        "Rate and review professionals",
      ],
      color:
        "border-blue-200 hover:border-blue-300 bg-blue-50/50 dark:bg-blue-950/20",
    },
    {
      id: "contractor" as UserRole,
      title: "Contractor",
      description: "I run a contracting business and want to find new clients",
      icon: <Briefcase className="h-8 w-8" />,
      features: [
        "Bid on projects",
        "Showcase your portfolio",
        "Manage client relationships",
        "Grow your business",
      ],
      color:
        "border-emerald-200 hover:border-emerald-300 bg-emerald-50/50 dark:bg-emerald-950/20",
    },
    {
      id: "day_laborer" as UserRole,
      title: "Day Laborer",
      description: "I provide skilled labor services and want to find work",
      icon: <Wrench className="h-8 w-8" />,
      features: [
        "Find daily work opportunities",
        "Set your hourly rates",
        "Build your reputation",
        "Get paid quickly",
      ],
      color:
        "border-orange-200 hover:border-orange-300 bg-orange-50/50 dark:bg-orange-950/20",
    },
  ];

  const handleRoleToggle = (roleId: UserRole) => {
    if (allowMultiple) {
      setSelectedRoles((prev) =>
        prev.includes(roleId)
          ? prev.filter((r) => r !== roleId)
          : [...prev, roleId]
      );
    } else {
      setSelectedRoles([roleId]);
    }
  };

  const handleSubmit = async () => {
    if (selectedRoles.length === 0) {
      dispatch(
        addNotification({
          type: "warning",
          message: "Please select at least one role to continue.",
          duration: 3000,
        })
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Create user roles one by one
      for (const role of selectedRoles) {
        await createUserRole({ role }).unwrap();
      }

      // Update Redux state with selected roles
      dispatch(setSelectedRoles(selectedRoles));
      dispatch(setUserRoles(selectedRoles));

      dispatch(
        addNotification({
          type: "success",
          message: `Successfully registered as ${selectedRoles
            .map((r) => r.replace("_", " "))
            .join(", ")}!`,
          duration: 5000,
        })
      );

      // Navigate to profile completion page
      router.push("/profile/complete");

      onComplete?.();
    } catch (error: any) {
      dispatch(
        addNotification({
          type: "error",
          message:
            error.error ||
            "Failed to save your role selection. Please try again.",
          duration: 5000,
        })
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          How do you want to use BuildPro?
        </h2>
        <p className="text-muted-foreground">
          {allowMultiple
            ? "Select all that apply - you can choose multiple roles"
            : "Choose the option that best describes you"}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {roles.map((role) => {
          const isSelected = selectedRoles.includes(role.id);

          return (
            <Card
              key={role.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? `ring-2 ring-orange-500 ${role.color}`
                  : `hover:shadow-md ${role.color}`
              }`}
              onClick={() => handleRoleToggle(role.id)}
            >
              <CardHeader className="text-center">
                <div className="mx-auto mb-2 p-3 rounded-full bg-white dark:bg-slate-800 shadow-sm">
                  {role.icon}
                </div>
                <CardTitle className="flex items-center justify-center gap-2">
                  {role.title}
                  {isSelected && (
                    <div className="p-1 bg-orange-500 rounded-full">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  )}
                </CardTitle>
                <CardDescription className="text-sm">
                  {role.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  {role.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => setSelectedRoles([])}
          disabled={selectedRoles.length === 0 || isSubmitting}
        >
          Clear Selection
        </Button>
        <Button
          className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
          onClick={handleSubmit}
          disabled={selectedRoles.length === 0 || isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Setting up your account...
            </>
          ) : (
            `Continue as ${
              selectedRoles.length > 0
                ? selectedRoles.map((r) => r.replace("_", " ")).join(" & ")
                : "..."
            }`
          )}
        </Button>
      </div>

      {allowMultiple && (
        <p className="text-xs text-muted-foreground text-center">
          You can always add or remove roles later in your profile settings.
        </p>
      )}
    </div>
  );
}
