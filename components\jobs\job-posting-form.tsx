'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useDispatch } from 'react-redux'
import { useRouter } from 'next/navigation'
import { useCreateJobPostingMutation, CreateJobPostingData } from '@/lib/store/api/jobsApi'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CalendarIcon, DollarSign, MapPin, Briefcase, Clock, AlertTriangle } from 'lucide-react'

const jobPostingSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  category: z.string().min(1, 'Please select a category'),
  budget_type: z.enum(['fixed', 'hourly', 'negotiable']),
  budget_min: z.number().min(0).optional(),
  budget_max: z.number().min(0).optional(),
  deadline: z.string().optional(),
  location_address: z.string().optional(),
  location_city: z.string().optional(),
  location_state: z.string().optional(),
  location_zip: z.string().optional(),
  urgency: z.enum(['low', 'normal', 'high', 'urgent']),
  requirements: z.array(z.string()).optional(),
})

type JobPostingFormData = z.infer<typeof jobPostingSchema>

interface JobPostingFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

export function JobPostingForm({ onSuccess, onCancel }: JobPostingFormProps) {
  const dispatch = useDispatch()
  const router = useRouter()
  const [createJobPosting, { isLoading }] = useCreateJobPostingMutation()
  const [customRequirement, setCustomRequirement] = useState('')

  const form = useForm<JobPostingFormData>({
    resolver: zodResolver(jobPostingSchema),
    defaultValues: {
      budget_type: 'fixed',
      urgency: 'normal',
      requirements: [],
    },
  })

  const categories = [
    'Plumbing',
    'Electrical',
    'HVAC',
    'Roofing',
    'Flooring',
    'Painting',
    'Landscaping',
    'Cleaning',
    'Handyman',
    'Kitchen Renovation',
    'Bathroom Renovation',
    'General Contracting',
    'Carpentry',
    'Drywall',
    'Tile Work',
    'Appliance Installation',
    'Fence Installation',
    'Deck Building',
    'Moving Services',
    'Other',
  ]

  const commonRequirements = [
    'Licensed and Insured',
    'Background Check Required',
    'References Required',
    'Portfolio/Photos Required',
    'Free Estimate',
    'Emergency Service Available',
    'Eco-Friendly Materials',
    'Warranty Provided',
    'Same Day Service',
    'Weekend Availability',
  ]

  const onSubmit = async (data: JobPostingFormData) => {
    try {
      const jobData: CreateJobPostingData = {
        ...data,
        requirements: data.requirements?.filter(req => req.trim() !== ''),
      }

      await createJobPosting(jobData).unwrap()

      dispatch(addNotification({
        type: 'success',
        message: 'Job posted successfully! Contractors will start seeing your project soon.',
        duration: 5000,
      }))

      onSuccess?.()
      router.push('/dashboard')
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to post job. Please try again.',
        duration: 5000,
      }))
    }
  }

  const addCustomRequirement = () => {
    if (customRequirement.trim()) {
      const currentRequirements = form.getValues('requirements') || []
      form.setValue('requirements', [...currentRequirements, customRequirement.trim()])
      setCustomRequirement('')
    }
  }

  const removeRequirement = (requirement: string) => {
    const currentRequirements = form.getValues('requirements') || []
    form.setValue('requirements', currentRequirements.filter(req => req !== requirement))
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
          Post a New Job
        </h1>
        <p className="text-muted-foreground mt-2">
          Describe your project and get quotes from qualified contractors
        </p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-orange-600" />
              Project Details
            </CardTitle>
            <CardDescription>
              Tell contractors about your project
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                placeholder="e.g., Kitchen Renovation, Bathroom Remodel, Deck Installation"
                {...form.register('title')}
              />
              {form.formState.errors.title && (
                <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select onValueChange={(value) => form.setValue('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select project category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.category && (
                <p className="text-sm text-red-600">{form.formState.errors.category.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Project Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your project in detail. Include materials, timeline, specific requirements, and any other relevant information..."
                rows={4}
                {...form.register('description')}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="urgency">Project Urgency</Label>
                <Select onValueChange={(value) => form.setValue('urgency', value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select urgency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low - Flexible timeline</SelectItem>
                    <SelectItem value="normal">Normal - Standard timeline</SelectItem>
                    <SelectItem value="high">High - Need soon</SelectItem>
                    <SelectItem value="urgent">Urgent - ASAP</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="deadline">Preferred Completion Date</Label>
                <Input
                  id="deadline"
                  type="date"
                  {...form.register('deadline')}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Budget Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              Budget Information
            </CardTitle>
            <CardDescription>
              Help contractors understand your budget expectations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="budget_type">Budget Type</Label>
              <Select onValueChange={(value) => form.setValue('budget_type', value as any)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select budget type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">Fixed Budget</SelectItem>
                  <SelectItem value="hourly">Hourly Rate</SelectItem>
                  <SelectItem value="negotiable">Negotiable</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {form.watch('budget_type') !== 'negotiable' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="budget_min">
                    Minimum Budget {form.watch('budget_type') === 'hourly' ? '(per hour)' : ''}
                  </Label>
                  <Input
                    id="budget_min"
                    type="number"
                    placeholder="0"
                    {...form.register('budget_min', { valueAsNumber: true })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget_max">
                    Maximum Budget {form.watch('budget_type') === 'hourly' ? '(per hour)' : ''}
                  </Label>
                  <Input
                    id="budget_max"
                    type="number"
                    placeholder="0"
                    {...form.register('budget_max', { valueAsNumber: true })}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-blue-600" />
              Project Location
            </CardTitle>
            <CardDescription>
              Where will the work be performed?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="location_address">Street Address</Label>
              <Input
                id="location_address"
                placeholder="123 Main Street"
                {...form.register('location_address')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location_city">City</Label>
                <Input
                  id="location_city"
                  placeholder="City"
                  {...form.register('location_city')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location_state">State</Label>
                <Input
                  id="location_state"
                  placeholder="State"
                  {...form.register('location_state')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location_zip">ZIP Code</Label>
                <Input
                  id="location_zip"
                  placeholder="12345"
                  {...form.register('location_zip')}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Project Requirements
            </CardTitle>
            <CardDescription>
              Select any specific requirements for contractors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {commonRequirements.map((requirement) => (
                <div key={requirement} className="flex items-center space-x-2">
                  <Checkbox
                    id={requirement}
                    checked={form.watch('requirements')?.includes(requirement)}
                    onCheckedChange={(checked) => {
                      const current = form.getValues('requirements') || []
                      if (checked) {
                        form.setValue('requirements', [...current, requirement])
                      } else {
                        form.setValue('requirements', current.filter(req => req !== requirement))
                      }
                    }}
                  />
                  <Label htmlFor={requirement} className="text-sm">
                    {requirement}
                  </Label>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <Label>Custom Requirements</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add custom requirement..."
                  value={customRequirement}
                  onChange={(e) => setCustomRequirement(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomRequirement())}
                />
                <Button type="button" onClick={addCustomRequirement}>
                  Add
                </Button>
              </div>
            </div>

            {form.watch('requirements') && form.watch('requirements')!.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Requirements:</Label>
                <div className="flex flex-wrap gap-2">
                  {form.watch('requirements')!.map((requirement) => (
                    <div
                      key={requirement}
                      className="flex items-center gap-1 bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-md text-sm"
                    >
                      {requirement}
                      <button
                        type="button"
                        onClick={() => removeRequirement(requirement)}
                        className="ml-1 text-orange-600 hover:text-orange-800"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-end">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
          >
            {isLoading ? 'Posting...' : 'Post Job'}
          </Button>
        </div>
      </form>
    </div>
  )
}
