"use client";

import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { RootState } from "@/lib/store";
import { setOnboardingStep } from "@/lib/store/slices/authSlice";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { EmailVerification } from "./email-verification";
import { RoleSelection } from "./role-selection";
// import { ProfileCompletion } from "../profile/profile-completion";

export function OnboardingModal() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { isAuthenticated, onboardingStep, profile, selectedRoles, user } =
    useSelector((state: RootState) => state.auth);

  // Only show modal if:
  // 1. User is authenticated
  // 2. Onboarding is not completed, not in general dashboard, and not in profile completion
  // 3. For email verification step, only show if email is not verified
  const shouldShowModal =
    isAuthenticated &&
    onboardingStep !== "completed" &&
    onboardingStep !== "general-dashboard" &&
    onboardingStep !== "profile-completion" &&
    // Only show email verification modal if email is not verified
    (onboardingStep !== "email-verification" || (profile && !profile.email_verified));

  const handleEmailVerified = () => {
    dispatch(setOnboardingStep("general-dashboard"));
  };

  const handleRoleSelectionComplete = () => {
    if (selectedRoles && selectedRoles.length > 0) {
      dispatch(setOnboardingStep("profile-completion"));
      router.push("/profile/complete");
    }
  };

  const getModalContent = () => {
    switch (onboardingStep) {
      case "email-verification":
        // Double-check that email is not verified before showing verification UI
        return profile && !profile.email_verified ? 
          <EmailVerification onVerified={handleEmailVerified} /> : null;
      case "role-selection":
        return <RoleSelection onComplete={handleRoleSelectionComplete} />;
      default:
        return null;
    }
  };

  if (!shouldShowModal) {
    return null;
  }

  const handleCloseModal = () => {
    // For email verification and role selection, don't allow closing via X button
    // Users can still sign out through the email verification component
  };

  return (
    <Dialog open={shouldShowModal} onOpenChange={handleCloseModal}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogTitle className="sr-only">Account Setup</DialogTitle>
        {getModalContent()}
      </DialogContent>
    </Dialog>
  );
}
