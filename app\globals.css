@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1280px;
  }

  .section-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .section-padding-sm {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .heading-1 {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
    line-height: 1.1;
  }

  .heading-2 {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
    line-height: 1.2;
  }

  .heading-3 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight;
    line-height: 1.3;
  }

  .body-large {
    @apply text-lg sm:text-xl leading-relaxed;
  }

  .body-medium {
    @apply text-base sm:text-lg leading-relaxed;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  .gradient-bg {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  }

  .gradient-bg-primary {
    background: linear-gradient(135deg, #f97316 0%, #dc2626 100%);
  }

  .gradient-bg-secondary {
    background: linear-gradient(135deg, #0f766e 0%, #059669 100%);
  }

  .gradient-bg-accent {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  }

  .glass-effect {
    @apply bg-white/90 dark:bg-slate-900/90 backdrop-blur-md border border-white/30;
  }

  .bg-grid-slate-100 {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }

  .bg-grid-slate-700\/25 {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(51 65 85 / 0.25)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Professional contractor theme colors */
  .text-contractor-primary {
    @apply text-orange-600 dark:text-orange-400;
  }

  .bg-contractor-primary {
    @apply bg-orange-600;
  }

  .border-contractor-primary {
    @apply border-orange-600;
  }

  .text-contractor-secondary {
    @apply text-teal-600 dark:text-teal-400;
  }

  .bg-contractor-secondary {
    @apply bg-teal-600;
  }

  .text-contractor-accent {
    @apply text-red-600 dark:text-red-400;
  }

  .bg-contractor-accent {
    @apply bg-red-600;
  }

  /* Enhanced hover effects for contractor theme */
  .hover-contractor-primary {
    @apply hover:text-orange-600 dark:hover:text-orange-400 hover:border-orange-300 dark:hover:border-orange-600;
  }

  .hover-contractor-bg {
    @apply hover:bg-orange-50 dark:hover:bg-orange-900/20;
  }
}
