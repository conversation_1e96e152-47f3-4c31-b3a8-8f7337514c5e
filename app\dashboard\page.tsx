"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/lib/store";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { RoleSwitcher } from "@/components/navigation/role-switcher";
import { NotificationCenter } from "@/components/navigation/notification-center";
import {
  HomeownerDashboard,
  ContractorDashboard,
  DayLaborerDashboard,
} from "@/components/dashboard/role-dashboards";
import { UserRole } from "@/lib/supabase/types";

export default function DashboardPage() {
  const router = useRouter();
  const {
    isAuthenticated,
    isLoading,
    profile,
    selectedRoles,
    activeRole,
    onboardingStep,
  } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/");
    }

    // If user is in profile completion step, redirect to the dedicated page
    if (isAuthenticated && onboardingStep === "profile-completion") {
      router.push("/profile/complete");
    }

    // If user has no roles selected, redirect to home for role selection
    if (
      isAuthenticated &&
      onboardingStep === "completed" &&
      (!selectedRoles || selectedRoles.length === 0)
    ) {
      router.push("/");
    }

    // Redirect to general dashboard for role selection
    if (isAuthenticated && selectedRoles && selectedRoles.length > 0) {
      router.push("/general-dashboard");
    }
  }, [isAuthenticated, isLoading, onboardingStep, selectedRoles, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !selectedRoles || selectedRoles.length === 0) {
    return null;
  }

  // Get current active role or default to first selected role
  const currentRole = activeRole || selectedRoles[0];

  const renderRoleDashboard = () => {
    switch (currentRole) {
      case "homeowner":
        return <HomeownerDashboard role={currentRole} />;
      case "contractor":
        return <ContractorDashboard role={currentRole} />;
      case "day_laborer":
        return <DayLaborerDashboard role={currentRole} />;
      default:
        return <HomeownerDashboard role="homeowner" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-slate-900 dark:text-white">
                BuildPro
              </h1>
              {selectedRoles.length > 0 && <RoleSwitcher />}
            </div>

            <div className="flex items-center gap-4">
              <NotificationCenter />
              <Button variant="outline" onClick={() => router.push("/")}>
                Back to Home
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderRoleDashboard()}
      </div>
    </div>
  );
}
