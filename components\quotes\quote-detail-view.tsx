"use client";

import React from "react";
import { Quote, QuoteItem, useGetQuoteQuery, useGetQuoteHistoryQuery } from "@/lib/store/api/quotesApi";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DollarSign,
  Calendar,
  FileText,
  User,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  History,
  Download,
  Print,
  Share,
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface QuoteDetailViewProps {
  quoteId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userRole?: 'homeowner' | 'contractor';
}

export function QuoteDetailView({ quoteId, open, onOpenChange, userRole = 'homeowner' }: QuoteDetailViewProps) {
  const { data: quote, isLoading, error } = useGetQuoteQuery(quoteId);
  const { data: history = [] } = useGetQuoteHistoryQuery(quoteId);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'expired':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'withdrawn':
        return <AlertCircle className="h-4 w-4" />;
      case 'expired':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-slate-200 rounded w-3/4"></div>
            <div className="h-4 bg-slate-200 rounded w-1/2"></div>
            <div className="space-y-3">
              <div className="h-4 bg-slate-200 rounded"></div>
              <div className="h-4 bg-slate-200 rounded w-2/3"></div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error || !quote) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>
              Failed to load quote details. Please try again.
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  const isExpired = quote.valid_until && new Date(quote.valid_until) < new Date();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold">{quote.title}</DialogTitle>
              <DialogDescription className="mt-2">
                Quote ID: {quote.id}
              </DialogDescription>
            </div>
            <div className="flex items-center gap-3">
              <Badge className={`${getStatusColor(quote.status)} flex items-center gap-1`}>
                {getStatusIcon(quote.status)}
                {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
              </Badge>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Print className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Share className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quote Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-emerald-600" />
                Quote Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 p-6 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-medium text-slate-700 dark:text-slate-300">Total Amount</span>
                  <span className="text-3xl font-bold text-emerald-600">
                    {formatCurrency(quote.total_amount)}
                  </span>
                </div>
                
                {(quote.labor_cost || quote.material_cost) && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {quote.labor_cost && (
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">Labor Cost:</span>
                        <span className="font-medium">{formatCurrency(quote.labor_cost)}</span>
                      </div>
                    )}
                    {quote.material_cost && (
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-slate-400">Material Cost:</span>
                        <span className="font-medium">{formatCurrency(quote.material_cost)}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          {quote.description && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Description
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                  {quote.description}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Quote Items */}
          {quote.items && quote.items.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Quote Breakdown</CardTitle>
                <CardDescription>Detailed breakdown of costs and materials</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {quote.items.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="text-xs">
                            {item.item_type}
                          </Badge>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {item.description}
                          </h4>
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          Quantity: {item.quantity} × {formatCurrency(item.unit_price)} each
                        </p>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-semibold text-slate-900 dark:text-white">
                          {formatCurrency(item.total_price)}
                        </span>
                      </div>
                    </div>
                  ))}
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                    <span className="text-lg font-semibold text-emerald-800 dark:text-emerald-200">
                      Items Subtotal
                    </span>
                    <span className="text-xl font-bold text-emerald-600">
                      {formatCurrency(quote.items.reduce((sum, item) => sum + item.total_price, 0))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Timeline */}
          {(quote.estimated_duration || quote.start_date || quote.end_date) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {quote.estimated_duration && (
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <Clock className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                      <p className="text-sm text-slate-600 dark:text-slate-400">Duration</p>
                      <p className="font-semibold">{quote.estimated_duration} days</p>
                    </div>
                  )}
                  {quote.start_date && (
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <Calendar className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                      <p className="text-sm text-slate-600 dark:text-slate-400">Start Date</p>
                      <p className="font-semibold">{new Date(quote.start_date).toLocaleDateString()}</p>
                    </div>
                  )}
                  {quote.end_date && (
                    <div className="text-center p-4 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <Calendar className="h-6 w-6 mx-auto mb-2 text-slate-600" />
                      <p className="text-sm text-slate-600 dark:text-slate-400">End Date</p>
                      <p className="font-semibold">{new Date(quote.end_date).toLocaleDateString()}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes and Terms */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {quote.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                    {quote.notes}
                  </p>
                </CardContent>
              </Card>
            )}

            {quote.terms_and_conditions && (
              <Card>
                <CardHeader>
                  <CardTitle>Terms & Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                    {quote.terms_and_conditions}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Quote History */}
          {history.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Quote History
                </CardTitle>
                <CardDescription>Status changes and updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {history.map((entry) => (
                    <div key={entry.id} className="flex items-start gap-3 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                      <div className="mt-1">
                        {getStatusIcon(entry.new_status)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-slate-900 dark:text-white">
                          Status changed to {entry.new_status}
                          {entry.old_status && ` from ${entry.old_status}`}
                        </p>
                        {entry.reason && (
                          <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                            Reason: {entry.reason}
                          </p>
                        )}
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          {new Date(entry.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quote Validity */}
          {quote.valid_until && (
            <Card className={isExpired ? "border-red-200 bg-red-50 dark:bg-red-900/20" : "border-orange-200 bg-orange-50 dark:bg-orange-900/20"}>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className={`h-5 w-5 ${isExpired ? 'text-red-500' : 'text-orange-500'}`} />
                  <span className={`font-medium ${isExpired ? 'text-red-700 dark:text-red-300' : 'text-orange-700 dark:text-orange-300'}`}>
                    {isExpired ? 'Quote Expired' : 'Quote Valid Until'}: {new Date(quote.valid_until).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quote Metadata */}
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-slate-500 dark:text-slate-400 space-y-1">
                <p>Quote created on {new Date(quote.created_at).toLocaleDateString()} at {new Date(quote.created_at).toLocaleTimeString()}</p>
                <p>Last updated on {new Date(quote.updated_at).toLocaleDateString()} at {new Date(quote.updated_at).toLocaleTimeString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
