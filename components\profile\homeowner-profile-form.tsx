'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useUpdateHomeownerProfileMutation } from '@/lib/store/api/profileApi'
import { useDispatch } from 'react-redux'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Home, MapPin, Ruler, Building, Loader2 } from 'lucide-react'

const homeownerProfileSchema = z.object({
  property_type: z.string().min(1, 'Please select a property type'),
  property_size: z.string().min(1, 'Please select a property size'),
  address: z.string().min(5, 'Please enter a valid address'),
  city: z.string().min(2, 'Please enter a valid city'),
  state: z.string().min(2, 'Please enter a valid state'),
  zip_code: z.string().regex(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code'),
})

type HomeownerProfileFormData = z.infer<typeof homeownerProfileSchema>

interface HomeownerProfileFormProps {
  onComplete?: () => void
  initialData?: Partial<HomeownerProfileFormData>
}

export function HomeownerProfileForm({ onComplete, initialData }: HomeownerProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const dispatch = useDispatch()
  const [updateHomeownerProfile] = useUpdateHomeownerProfileMutation()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<HomeownerProfileFormData>({
    resolver: zodResolver(homeownerProfileSchema),
    defaultValues: initialData,
  })

  const propertyTypes = [
    { value: 'single_family', label: 'Single Family Home' },
    { value: 'townhouse', label: 'Townhouse' },
    { value: 'condo', label: 'Condominium' },
    { value: 'apartment', label: 'Apartment' },
    { value: 'duplex', label: 'Duplex' },
    { value: 'mobile_home', label: 'Mobile Home' },
    { value: 'other', label: 'Other' },
  ]

  const propertySizes = [
    { value: 'under_1000', label: 'Under 1,000 sq ft' },
    { value: '1000_1500', label: '1,000 - 1,500 sq ft' },
    { value: '1500_2000', label: '1,500 - 2,000 sq ft' },
    { value: '2000_2500', label: '2,000 - 2,500 sq ft' },
    { value: '2500_3000', label: '2,500 - 3,000 sq ft' },
    { value: '3000_4000', label: '3,000 - 4,000 sq ft' },
    { value: 'over_4000', label: 'Over 4,000 sq ft' },
  ]

  const onSubmit = async (data: HomeownerProfileFormData) => {
    setIsSubmitting(true)

    try {
      await updateHomeownerProfile({
        ...data,
        profile_completed: true,
      }).unwrap()

      dispatch(addNotification({
        type: 'success',
        message: 'Homeowner profile completed successfully!',
        duration: 5000,
      }))

      onComplete?.()
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to save profile. Please try again.',
        duration: 5000,
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
          <Home className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
        <CardTitle className="text-2xl">Complete Your Homeowner Profile</CardTitle>
        <CardDescription>
          Tell us about your property so we can match you with the right contractors
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Property Type */}
          <div className="space-y-2">
            <Label htmlFor="property_type">Property Type</Label>
            <Select onValueChange={(value) => setValue('property_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select your property type" />
              </SelectTrigger>
              <SelectContent>
                {propertyTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      {type.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.property_type && (
              <p className="text-sm text-red-600">{errors.property_type.message}</p>
            )}
          </div>

          {/* Property Size */}
          <div className="space-y-2">
            <Label htmlFor="property_size">Property Size</Label>
            <Select onValueChange={(value) => setValue('property_size', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select your property size" />
              </SelectTrigger>
              <SelectContent>
                {propertySizes.map((size) => (
                  <SelectItem key={size.value} value={size.value}>
                    <div className="flex items-center gap-2">
                      <Ruler className="h-4 w-4" />
                      {size.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.property_size && (
              <p className="text-sm text-red-600">{errors.property_size.message}</p>
            )}
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label htmlFor="address">Street Address</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="address"
                type="text"
                placeholder="123 Main Street"
                className="pl-10"
                {...register('address')}
              />
            </div>
            {errors.address && (
              <p className="text-sm text-red-600">{errors.address.message}</p>
            )}
          </div>

          {/* City, State, ZIP */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                type="text"
                placeholder="City"
                {...register('city')}
              />
              {errors.city && (
                <p className="text-sm text-red-600">{errors.city.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                type="text"
                placeholder="State"
                {...register('state')}
              />
              {errors.state && (
                <p className="text-sm text-red-600">{errors.state.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="zip_code">ZIP Code</Label>
              <Input
                id="zip_code"
                type="text"
                placeholder="12345"
                {...register('zip_code')}
              />
              {errors.zip_code && (
                <p className="text-sm text-red-600">{errors.zip_code.message}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              className="flex-1"
              onClick={() => onComplete?.()}
            >
              Skip for Now
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving Profile...
                </>
              ) : (
                'Complete Profile'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
