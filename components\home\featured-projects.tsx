import { ArrowRight } from "lucide-react";
import { Project } from "@/lib/types";
import { featuredProjects } from "@/lib/data";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export function FeaturedProjects() {
  return (
    <section className="section-padding bg-teal-50 dark:bg-teal-950/10">
      <div className="container">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="heading-2 mb-4">Latest Projects</h2>
          <p className="body-medium text-muted-foreground max-w-2xl mx-auto">
            Explore the best projects that clients are looking to hire for and
            find your next opportunity
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
          {featuredProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        <div className="flex justify-center mt-12 lg:mt-16">
          <Link href="/projects">
            <Button
              variant="outline"
              className="group px-8 py-3 text-base font-semibold rounded-xl hover:shadow-md transition-all duration-200"
            >
              Explore All Projects
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}

function ProjectCard({ project }: { project: Project }) {
  return (
    <div className="rounded-2xl border bg-white dark:bg-slate-800 overflow-hidden card-hover">
      <div className="p-6 lg:p-8">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 className="font-semibold text-lg lg:text-xl mb-2">
              <Link
                href={`/projects/${project.id}`}
                className="hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
              >
                {project.title}
              </Link>
            </h3>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className="flex items-center">
                Posted by{" "}
                <span className="font-medium ml-1">{project.ownerName}</span>
              </span>
              <span className="mx-2">•</span>
              <span>{formatDate(project.postedAt)}</span>
            </div>
          </div>
          <Link href={`/projects/${project.id}`}>
            <Button
              variant="outline"
              size="sm"
              className="text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-700 hover:bg-orange-50 dark:hover:bg-orange-900/20"
            >
              View
            </Button>
          </Link>
        </div>

        <p className="text-muted-foreground text-sm line-clamp-2 mb-4">
          {project.description}
        </p>

        <div className="flex flex-wrap gap-2 mb-6">
          {project.skills.slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="text-xs bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-800 dark:text-orange-300 px-3 py-1.5 rounded-full font-medium"
            >
              {skill}
            </span>
          ))}
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm mb-6">
          <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
            <p className="text-muted-foreground text-xs mb-1">Budget</p>
            <p className="font-semibold text-gray-900 dark:text-white">
              ${project.budget.min} - ${project.budget.max}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
            <p className="text-muted-foreground text-xs mb-1">Location</p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {project.location}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
            <p className="text-muted-foreground text-xs mb-1">Proposals</p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {project.proposals} received
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
            <p className="text-muted-foreground text-xs mb-1">Deadline</p>
            <p className="font-semibold text-gray-900 dark:text-white">
              {formatDate(project.deadline)}
            </p>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <Link href={`/projects/${project.id}/proposals/new`}>
            <Button className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 rounded-xl transition-all duration-200 hover:shadow-lg">
              Submit Proposal
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

function formatDate(date?: Date): string {
  if (!date) return "N/A";
  return new Date(date).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}
