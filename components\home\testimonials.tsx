import { ChevronLeft, ChevronRight } from "lucide-react";
import { testimonials } from "@/lib/data";
import { Button } from "@/components/ui/button";

export function Testimonials() {
  return (
    <section className="section-padding bg-gradient-to-br from-slate-50 via-orange-50 to-red-50 dark:from-slate-950/10 dark:via-orange-950/10 dark:to-red-950/10">
      <div className="container">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="heading-2 mb-4">What Our Users Say</h2>
          <p className="body-medium text-muted-foreground mx-auto max-w-2xl">
            Hear from homeowners and contractors who have found success on our
            platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-2xl p-6 lg:p-8 card-hover"
            >
              <div className="flex items-center space-x-1 mb-6">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className={`w-5 h-5 ${
                      star <= Math.floor(testimonial.rating)
                        ? "text-amber-500 fill-current"
                        : star - 0.5 <= testimonial.rating
                        ? "text-amber-500 fill-current"
                        : "text-gray-300 dark:text-gray-600"
                    }`}
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" />
                  </svg>
                ))}
              </div>

              <blockquote className="text-lg leading-relaxed mb-6 text-slate-700 dark:text-slate-300">
                "{testimonial.text}"
              </blockquote>

              <div className="flex items-center">
                <img
                  src={testimonial.authorAvatar}
                  alt={testimonial.authorName}
                  className="w-14 h-14 rounded-full object-cover mr-4 border-2 border-slate-200 dark:border-slate-700"
                />
                <div>
                  <p className="font-semibold text-slate-900 dark:text-white">
                    {testimonial.authorName}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {testimonial.authorRole}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-12 lg:mt-16">
          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
