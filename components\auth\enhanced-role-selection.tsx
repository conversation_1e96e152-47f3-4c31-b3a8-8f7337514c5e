"use client";

import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { RootState } from "@/lib/store";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Home,
  Hammer,
  Briefcase,
  CheckCircle,
  ArrowRight,
  Star,
  TrendingUp,
  Clock,
  DollarSign,
} from "lucide-react";
import { useCreateUserRoleMutation } from "@/lib/store/api/profileApi";
import {
  addNotification,
  closeRoleSelectionModal,
} from "@/lib/store/slices/uiSlice";
import { setOnboardingStep } from "@/lib/store/slices/authSlice";

interface EnhancedRoleSelectionProps {
  onComplete?: () => void;
  showSkipOption?: boolean;
}

export function EnhancedRoleSelection({
  onComplete,
  showSkipOption = true,
}: EnhancedRoleSelectionProps) {
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.auth);
  const [createUserRole] = useCreateUserRoleMutation();

  const roles = [
    {
      id: "homeowner",
      title: "Homeowner",
      description: "Post projects and hire skilled professionals",
      icon: Home,
      color:
        "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",
      iconColor: "text-blue-600 dark:text-blue-400",
      benefits: [
        "Post unlimited projects",
        "Get competitive quotes",
        "Secure payment protection",
        "Rate and review contractors",
      ],
      stats: {
        projects: "15,000+ projects posted",
        satisfaction: "4.8/5 satisfaction rate",
        savings: "Save up to 30% on costs",
      },
    },
    {
      id: "contractor",
      title: "Contractor",
      description: "Grow your business with quality projects",
      icon: Hammer,
      color:
        "bg-emerald-50 dark:bg-emerald-950/20 border-emerald-200 dark:border-emerald-800",
      iconColor: "text-emerald-600 dark:text-emerald-400",
      benefits: [
        "Access to verified projects",
        "Build your reputation",
        "Flexible bidding system",
        "Secure payment guarantee",
      ],
      stats: {
        projects: "8,500+ active contractors",
        earnings: "Avg. $75k+ annual revenue",
        growth: "40% business growth",
      },
    },
    {
      id: "day_laborer",
      title: "Day Laborer",
      description: "Find flexible work that fits your schedule",
      icon: Briefcase,
      color:
        "bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800",
      iconColor: "text-orange-600 dark:text-orange-400",
      benefits: [
        "Flexible work schedule",
        "Quick daily payments",
        "Local opportunities",
        "Skill-based matching",
      ],
      stats: {
        projects: "3,200+ day laborers",
        rate: "$25-45/hour average",
        availability: "Work when you want",
      },
    },
  ];

  const handleRoleToggle = (roleId: string) => {
    setSelectedRoles((prev) =>
      prev.includes(roleId)
        ? prev.filter((id) => id !== roleId)
        : [...prev, roleId]
    );
  };

  const handleSubmit = async () => {
    if (selectedRoles.length === 0) {
      dispatch(
        addNotification({
          type: "warning",
          message: "Please select at least one role to continue.",
          duration: 3000,
        })
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Create user roles for each selected role
      for (const role of selectedRoles) {
        await createUserRole({
          user_id: user!.id,
          role: role as "homeowner" | "contractor" | "day_laborer",
        }).unwrap();
      }

      dispatch(
        addNotification({
          type: "success",
          message: `Successfully selected ${selectedRoles.length} role(s)! Let's complete your profile.`,
          duration: 3000,
        })
      );

      // Move to profile completion
      dispatch(setOnboardingStep("profile-completion"));
      dispatch(closeRoleSelectionModal());

      // Navigate to profile completion page
      router.push("/profile/complete");

      onComplete?.();
    } catch (error: any) {
      dispatch(
        addNotification({
          type: "error",
          message: error.error || "Failed to save roles. Please try again.",
          duration: 5000,
        })
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    dispatch(closeRoleSelectionModal());
    dispatch(
      addNotification({
        type: "info",
        message:
          "You can select your roles anytime from your profile settings.",
        duration: 3000,
      })
    );
    onComplete?.();
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
          Choose Your Role(s)
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Select one or more roles that best describe how you want to use
          BuildPro. You can always add more roles later.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {roles.map((role) => (
          <Card
            key={role.id}
            className={`${
              role.color
            } cursor-pointer transition-all duration-300 hover:shadow-lg ${
              selectedRoles.includes(role.id)
                ? "ring-2 ring-orange-500 dark:ring-orange-400"
                : ""
            }`}
            onClick={() => handleRoleToggle(role.id)}
          >
            <CardHeader className="text-center relative">
              <div className="absolute top-4 right-4">
                <Checkbox
                  checked={selectedRoles.includes(role.id)}
                  onChange={() => handleRoleToggle(role.id)}
                  className="data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                />
              </div>

              <div
                className={`w-16 h-16 mx-auto rounded-full bg-white dark:bg-slate-800 flex items-center justify-center mb-4 ${
                  selectedRoles.includes(role.id) ? "scale-110" : ""
                } transition-transform`}
              >
                <role.icon className={`h-8 w-8 ${role.iconColor}`} />
              </div>

              <CardTitle className="text-xl">{role.title}</CardTitle>
              <CardDescription className="text-base">
                {role.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-sm text-slate-900 dark:text-white">
                  Benefits:
                </h4>
                <ul className="space-y-1">
                  {role.benefits.map((benefit, index) => (
                    <li
                      key={index}
                      className="flex items-center gap-2 text-sm text-muted-foreground"
                    >
                      <CheckCircle className="h-3 w-3 text-emerald-600 dark:text-emerald-400 flex-shrink-0" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="pt-3 border-t border-slate-200 dark:border-slate-700">
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {role.stats.projects || role.stats.earnings}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    {role.stats.satisfaction || role.stats.rate}
                  </div>
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    {role.stats.savings ||
                      role.stats.growth ||
                      role.stats.availability}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedRoles.length > 0 && (
        <div className="bg-orange-50 dark:bg-orange-950/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            <h3 className="font-semibold text-slate-900 dark:text-white">
              Selected Roles ({selectedRoles.length})
            </h3>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedRoles.map((roleId) => {
              const role = roles.find((r) => r.id === roleId);
              return (
                <Badge
                  key={roleId}
                  variant="secondary"
                  className="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200"
                >
                  {role?.title}
                </Badge>
              );
            })}
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {showSkipOption && (
          <Button
            variant="outline"
            onClick={handleSkip}
            className="order-2 sm:order-1"
          >
            Skip for Now
          </Button>
        )}

        <Button
          onClick={handleSubmit}
          disabled={selectedRoles.length === 0 || isSubmitting}
          className="order-1 sm:order-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 min-w-[200px]"
        >
          {isSubmitting ? (
            <>
              <Clock className="h-4 w-4 animate-spin mr-2" />
              Setting up roles...
            </>
          ) : (
            <>
              Continue with {selectedRoles.length} role
              {selectedRoles.length !== 1 ? "s" : ""}
              <ArrowRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>
          Don't worry! You can always add or remove roles later from your
          profile settings.
        </p>
      </div>
    </div>
  );
}
