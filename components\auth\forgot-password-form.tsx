'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useResetPasswordMutation } from '@/lib/store/api/authApi'
import { useDispatch } from 'react-redux'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Mail, ArrowLeft, Loader2, CheckCircle } from 'lucide-react'

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

interface ForgotPasswordFormProps {
  onSuccess?: () => void
  onBackToSignIn?: () => void
}

export function ForgotPasswordForm({ onSuccess, onBackToSignIn }: ForgotPasswordFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailSent, setEmailSent] = useState(false)
  const [sentEmail, setSentEmail] = useState('')
  const dispatch = useDispatch()
  const [resetPassword] = useResetPasswordMutation()

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  })

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsSubmitting(true)

    try {
      await resetPassword({ email: data.email }).unwrap()
      setEmailSent(true)
      setSentEmail(data.email)
      dispatch(addNotification({
        type: 'success',
        message: 'Password reset email sent! Please check your inbox.',
        duration: 5000,
      }))
      onSuccess?.()
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to send reset email. Please try again.',
        duration: 5000,
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleResendEmail = async () => {
    const email = getValues('email') || sentEmail
    if (!email) return

    setIsSubmitting(true)
    try {
      await resetPassword({ email }).unwrap()
      dispatch(addNotification({
        type: 'success',
        message: 'Password reset email sent again!',
        duration: 3000,
      }))
    } catch (error: any) {
      dispatch(addNotification({
        type: 'error',
        message: error.error || 'Failed to resend email. Please try again.',
        duration: 5000,
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  if (emailSent) {
    return (
      <div className="space-y-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-emerald-100 dark:bg-emerald-900/20 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-emerald-600 dark:text-emerald-400" />
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
              Check Your Email
            </h2>
            <p className="text-muted-foreground">
              We've sent a password reset link to{' '}
              <span className="font-medium text-slate-900 dark:text-white">
                {sentEmail}
              </span>
            </p>
          </div>
        </div>

        <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Mail className="h-4 w-4" />
            <span>Click the link in the email to reset your password</span>
          </div>
          <div className="text-xs text-muted-foreground">
            Don't forget to check your spam folder if you don't see the email in your inbox.
          </div>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-muted-foreground text-center">
            Didn't receive the email?
          </p>
          <Button
            variant="outline"
            onClick={handleResendEmail}
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Resend Email
              </>
            )}
          </Button>
        </div>

        <div className="text-center">
          <Button
            variant="ghost"
            onClick={onBackToSignIn}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Sign In
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
          Reset Your Password
        </h2>
        <p className="text-muted-foreground">
          Enter your email address and we'll send you a link to reset your password
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="email"
              type="email"
              placeholder="Enter your email address"
              className="pl-10"
              {...register('email')}
            />
          </div>
          {errors.email && (
            <p className="text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Sending Reset Email...
            </>
          ) : (
            <>
              <Mail className="h-4 w-4 mr-2" />
              Send Reset Email
            </>
          )}
        </Button>
      </form>

      <div className="text-center">
        <Button
          variant="ghost"
          onClick={onBackToSignIn}
          className="text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Sign In
        </Button>
      </div>
    </div>
  )
}
