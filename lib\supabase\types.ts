export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          phone: string | null
          created_at: string
          updated_at: string
          email_verified: boolean
          onboarding_completed: boolean
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
          email_verified?: boolean
          onboarding_completed?: boolean
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
          email_verified?: boolean
          onboarding_completed?: boolean
        }
      }
      user_roles: {
        Row: {
          id: string
          user_id: string
          role: 'homeowner' | 'contractor' | 'day_laborer'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          role: 'homeowner' | 'contractor' | 'day_laborer'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          role?: 'homeowner' | 'contractor' | 'day_laborer'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      homeowner_profiles: {
        Row: {
          id: string
          user_id: string
          property_type: string | null
          property_size: string | null
          address: string | null
          city: string | null
          state: string | null
          zip_code: string | null
          profile_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          property_type?: string | null
          property_size?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          property_type?: string | null
          property_size?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      contractor_profiles: {
        Row: {
          id: string
          user_id: string
          business_name: string | null
          license_number: string | null
          insurance_verified: boolean
          specializations: string[] | null
          years_experience: number | null
          service_radius: number | null
          hourly_rate: number | null
          profile_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          business_name?: string | null
          license_number?: string | null
          insurance_verified?: boolean
          specializations?: string[] | null
          years_experience?: number | null
          service_radius?: number | null
          hourly_rate?: number | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          business_name?: string | null
          license_number?: string | null
          insurance_verified?: boolean
          specializations?: string[] | null
          years_experience?: number | null
          service_radius?: number | null
          hourly_rate?: number | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      day_laborer_profiles: {
        Row: {
          id: string
          user_id: string
          skills: string[] | null
          availability: Json | null
          hourly_rate: number | null
          transportation: boolean
          tools_owned: string[] | null
          profile_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          skills?: string[] | null
          availability?: Json | null
          hourly_rate?: number | null
          transportation?: boolean
          tools_owned?: string[] | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          skills?: string[] | null
          availability?: Json | null
          hourly_rate?: number | null
          transportation?: boolean
          tools_owned?: string[] | null
          profile_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'homeowner' | 'contractor' | 'day_laborer'
    }
  }
}

export type UserRole = 'homeowner' | 'contractor' | 'day_laborer'

export type Profile = Database['public']['Tables']['profiles']['Row']
export type UserRoleRecord = Database['public']['Tables']['user_roles']['Row']
export type HomeownerProfile = Database['public']['Tables']['homeowner_profiles']['Row']
export type ContractorProfile = Database['public']['Tables']['contractor_profiles']['Row']
export type DayLaborerProfile = Database['public']['Tables']['day_laborer_profiles']['Row']
