import { createApi, fakeBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "@/lib/supabase/client";
import { User, AuthError } from "@supabase/supabase-js";

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User | null;
  error: AuthError | null;
}

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fakeBaseQuery(),
  tagTypes: ["Auth"],
  endpoints: (builder) => ({
    signUp: builder.mutation<AuthResponse, SignUpData>({
      queryFn: async ({ email, password, fullName }) => {
        try {
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                full_name: fullName,
              },
              emailRedirectTo: `${window.location.origin}/auth/callback`,
            },
          });

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { user: data.user, error: null } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Auth"],
    }),

    signIn: builder.mutation<AuthResponse, SignInData>({
      queryFn: async ({ email, password }) => {
        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { user: data.user, error: null } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Auth"],
    }),

    signInWithGoogle: builder.mutation<AuthResponse, void>({
      queryFn: async () => {
        try {
          const { error } = await supabase.auth.signInWithOAuth({
            provider: "google",
            options: {
              redirectTo: `${window.location.origin}/auth/callback`,
            },
          });

          if (error) {
            return { error: { error: error.message } };
          }

          // OAuth returns a redirect URL, not a user object
          return { data: { user: null, error: null } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Auth"],
    }),

    signOut: builder.mutation<{ success: boolean }, void>({
      queryFn: async () => {
        try {
          const { error } = await supabase.auth.signOut();

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { success: true } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Auth"],
    }),

    resendEmailVerification: builder.mutation<
      { success: boolean },
      { email: string }
    >({
      queryFn: async ({ email }) => {
        try {
          const { error } = await supabase.auth.resend({
            type: "signup",
            email,
            options: {
              emailRedirectTo: `${window.location.origin}/auth/callback`,
            },
          });

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { success: true } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
    }),

    resetPassword: builder.mutation<{ success: boolean }, { email: string }>({
      queryFn: async ({ email }) => {
        try {
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`,
          });

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { success: true } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
    }),

    updatePassword: builder.mutation<
      { success: boolean },
      { password: string }
    >({
      queryFn: async ({ password }) => {
        try {
          const { error } = await supabase.auth.updateUser({
            password,
          });

          if (error) {
            return { error: { error: error.message } };
          }

          return { data: { success: true } };
        } catch (error) {
          return { error: { error: "An unexpected error occurred" } };
        }
      },
      invalidatesTags: ["Auth"],
    }),
  }),
});

export const {
  useSignUpMutation,
  useSignInMutation,
  useSignInWithGoogleMutation,
  useSignOutMutation,
  useResendEmailVerificationMutation,
  useResetPasswordMutation,
  useUpdatePasswordMutation,
} = authApi;
