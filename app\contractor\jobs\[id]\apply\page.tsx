"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  DollarSign,
  Clock,
  MapPin,
  Star,
  CheckCircle,
  Upload,
  FileText,
  Calendar,
  Users,
  Award,
  AlertCircle,
  Send,
} from "lucide-react";

const applicationSchema = z.object({
  quote: z.number().min(1, "Quote amount is required"),
  timeline: z.string().min(1, "Timeline is required"),
  proposal: z.string().min(50, "Proposal must be at least 50 characters"),
  experience: z.string().min(20, "Please describe your relevant experience"),
  availability: z.string().min(1, "Availability is required"),
});

type ApplicationData = z.infer<typeof applicationSchema>;

// Mock job data - in real app, this would come from API
const mockJob = {
  id: "1",
  title: "Kitchen Renovation - Modern Design",
  description:
    "Looking for an experienced contractor to renovate our 200 sq ft kitchen. Need complete overhaul including cabinets, countertops, flooring, and electrical work.",
  category: "Kitchen Renovation",
  location: "San Francisco, CA",
  budget: { min: 15000, max: 25000, type: "fixed" as const },
  timeline: "6-8 weeks",
  postedDate: "2024-01-15",
  applicants: 12,
  homeowner: {
    name: "Sarah Johnson",
    rating: 4.8,
    reviewsCount: 23,
    verified: true,
  },
  requirements: [
    "Licensed contractor",
    "5+ years experience",
    "Portfolio required",
  ],
  tags: ["Kitchen", "Renovation", "Modern", "High-end"],
  urgency: "medium" as const,
  details: {
    scope: [
      "Remove existing cabinets and appliances",
      "Install new custom cabinets",
      "Granite countertop installation",
      "Hardwood flooring installation",
      "Electrical work for new appliances",
      "Plumbing for new sink location",
    ],
    materials: "Homeowner will provide materials",
    permits: "Contractor responsible for permits",
    insurance: "Proof of insurance required",
  },
};

export default function JobApplicationPage() {
  const router = useRouter();
  const params = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [portfolioFiles, setPortfolioFiles] = useState<File[]>([]);

  const form = useForm<ApplicationData>({
    resolver: zodResolver(applicationSchema),
    defaultValues: {
      quote: 0,
      timeline: "",
      proposal: "",
      experience: "",
      availability: "",
    },
  });

  const onSubmit = async (data: ApplicationData) => {
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("Application submitted:", data);
    console.log("Portfolio files:", portfolioFiles);

    setIsSubmitting(false);
    router.push("/contractor/jobs?applied=true");
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setPortfolioFiles((prev) => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setPortfolioFiles((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-emerald-200/50 dark:border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Jobs
              </Button>
            </div>
            <div className="text-right">
              <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                Apply for Job
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Submit your proposal
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Job Details Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8 border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-t-lg">
                <CardTitle className="text-lg">{mockJob.title}</CardTitle>
                <CardDescription className="text-emerald-100">
                  {mockJob.category}
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6 space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-slate-500" />
                    <span>{mockJob.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-slate-500" />
                    <span>
                      ${mockJob.budget.min.toLocaleString()} - $
                      {mockJob.budget.max.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-slate-500" />
                    <span>{mockJob.timeline}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-slate-500" />
                    <span>{mockJob.applicants} applicants</span>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-2">Homeowner</h4>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center text-white font-medium">
                      {mockJob.homeowner.name.charAt(0)}
                    </div>
                    <div>
                      <p className="font-medium">{mockJob.homeowner.name}</p>
                      <div className="flex items-center gap-1 text-sm">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span>{mockJob.homeowner.rating}</span>
                        <span className="text-slate-500">
                          ({mockJob.homeowner.reviewsCount})
                        </span>
                        {mockJob.homeowner.verified && (
                          <CheckCircle className="h-3 w-3 text-emerald-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-2">Requirements</h4>
                  <ul className="space-y-1">
                    {mockJob.requirements.map((req, index) => (
                      <li
                        key={index}
                        className="text-sm flex items-center gap-2"
                      >
                        <CheckCircle className="h-3 w-3 text-emerald-500" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-2">Project Scope</h4>
                  <ul className="space-y-1">
                    {mockJob.details.scope.map((item, index) => (
                      <li
                        key={index}
                        className="text-sm flex items-start gap-2"
                      >
                        <div className="w-1 h-1 bg-emerald-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Application Form */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Submit Your Proposal</CardTitle>
                <CardDescription>
                  Provide detailed information about your quote and approach to
                  win this project.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  {/* Quote Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="quote">Your Quote ($)</Label>
                      <Input
                        id="quote"
                        type="number"
                        placeholder="Enter your quote"
                        {...form.register("quote", { valueAsNumber: true })}
                        className="text-lg font-semibold"
                      />
                      {form.formState.errors.quote && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.quote.message}
                        </p>
                      )}
                      <p className="text-xs text-slate-500">
                        Budget range: ${mockJob.budget.min.toLocaleString()} - $
                        {mockJob.budget.max.toLocaleString()}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timeline">Estimated Timeline</Label>
                      <Input
                        id="timeline"
                        placeholder="e.g., 6-8 weeks"
                        {...form.register("timeline")}
                      />
                      {form.formState.errors.timeline && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.timeline.message}
                        </p>
                      )}
                      <p className="text-xs text-slate-500">
                        Requested timeline: {mockJob.timeline}
                      </p>
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="space-y-2">
                    <Label htmlFor="availability">Availability</Label>
                    <Input
                      id="availability"
                      placeholder="When can you start? e.g., Available immediately, Can start in 2 weeks"
                      {...form.register("availability")}
                    />
                    {form.formState.errors.availability && (
                      <p className="text-sm text-red-600">
                        {form.formState.errors.availability.message}
                      </p>
                    )}
                  </div>

                  {/* Proposal */}
                  <div className="space-y-2">
                    <Label htmlFor="proposal">Project Proposal</Label>
                    <Textarea
                      id="proposal"
                      placeholder="Describe your approach to this project, materials you'll use, and why you're the best choice..."
                      rows={6}
                      {...form.register("proposal")}
                    />
                    {form.formState.errors.proposal && (
                      <p className="text-sm text-red-600">
                        {form.formState.errors.proposal.message}
                      </p>
                    )}
                  </div>

                  {/* Experience */}
                  <div className="space-y-2">
                    <Label htmlFor="experience">Relevant Experience</Label>
                    <Textarea
                      id="experience"
                      placeholder="Describe your experience with similar projects, certifications, and any special qualifications..."
                      rows={4}
                      {...form.register("experience")}
                    />
                    {form.formState.errors.experience && (
                      <p className="text-sm text-red-600">
                        {form.formState.errors.experience.message}
                      </p>
                    )}
                  </div>

                  {/* Portfolio Upload */}
                  <div className="space-y-4">
                    <Label>Portfolio & References</Label>
                    <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 mx-auto text-slate-400 mb-2" />
                      <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                        Upload photos of similar work, certificates, or
                        references
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*,.pdf"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="portfolio-upload"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() =>
                          document.getElementById("portfolio-upload")?.click()
                        }
                      >
                        Choose Files
                      </Button>
                    </div>

                    {portfolioFiles.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Uploaded Files:</p>
                        {portfolioFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-700 rounded"
                          >
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4" />
                              <span className="text-sm">{file.name}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Important Notes */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div className="space-y-2">
                        <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                          Important Notes
                        </h4>
                        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                          <li>• {mockJob.details.materials}</li>
                          <li>• {mockJob.details.permits}</li>
                          <li>• {mockJob.details.insurance}</li>
                          <li>
                            • Payment will be held in escrow until project
                            completion
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex flex-col sm:flex-row gap-4 pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                      className="sm:w-auto"
                    >
                      Save as Draft
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Submitting Application...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Submit Application
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
