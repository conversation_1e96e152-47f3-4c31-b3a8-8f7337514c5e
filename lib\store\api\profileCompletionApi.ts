import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { supabase } from '@/lib/supabase/client'

// Types for profile completion
export interface HomeownerProfileData {
  property_type?: string
  property_size?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  preferred_service_categories?: string[]
  budget_range?: string
  communication_preferences?: string[]
  notification_settings?: string[]
  service_area_radius?: number
  preferred_contact_times?: string[]
  profile_completed?: boolean
}

export interface ContractorProfileData {
  business_name?: string
  company_registration?: string
  license_number?: string
  insurance_verified?: boolean
  insurance_provider?: string
  insurance_policy_number?: string
  business_description?: string
  specializations?: string[]
  service_categories?: string[]
  years_experience?: number
  service_radius?: number
  hourly_rate?: number
  project_size_preferences?: string[]
  availability_status?: string
  response_time?: string
  certifications?: string[]
  travel_radius?: number
  profile_completed?: boolean
}

export interface DayLaborerProfileData {
  skill_categories?: string[]
  years_experience?: number
  preferred_job_types?: string[]
  physical_capabilities?: string[]
  hourly_rate?: number
  availability_status?: string
  transportation?: string
  travel_radius?: number
  tools_owned?: string[]
  certifications?: string[]
  profile_completed?: boolean
}

export const profileCompletionApi = createApi({
  reducerPath: 'profileCompletionApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: async (headers) => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.access_token) {
        headers.set('authorization', `Bearer ${session.access_token}`)
      }
      return headers
    },
  }),
  tagTypes: ['HomeownerProfile', 'ContractorProfile', 'DayLaborerProfile'],
  endpoints: (builder) => ({
    // Homeowner Profile Endpoints
    getHomeownerProfile: builder.query<HomeownerProfileData | null, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('homeowner_profiles')
            .select('*')
            .eq('user_id', user.id)
            .single()

          if (error && error.code !== 'PGRST116') throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['HomeownerProfile'],
    }),

    updateHomeownerProfile: builder.mutation<HomeownerProfileData, Partial<HomeownerProfileData>>({
      queryFn: async (profileData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const updateData = {
            ...profileData,
            user_id: user.id,
            updated_at: new Date().toISOString(),
          }

          const { data, error } = await supabase
            .from('homeowner_profiles')
            .upsert(updateData, { onConflict: 'user_id' })
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['HomeownerProfile'],
    }),

    // Contractor Profile Endpoints
    getContractorProfile: builder.query<ContractorProfileData | null, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('contractor_profiles')
            .select('*')
            .eq('user_id', user.id)
            .single()

          if (error && error.code !== 'PGRST116') throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['ContractorProfile'],
    }),

    updateContractorProfile: builder.mutation<ContractorProfileData, Partial<ContractorProfileData>>({
      queryFn: async (profileData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const updateData = {
            ...profileData,
            user_id: user.id,
            updated_at: new Date().toISOString(),
          }

          const { data, error } = await supabase
            .from('contractor_profiles')
            .upsert(updateData, { onConflict: 'user_id' })
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['ContractorProfile'],
    }),

    // Day Laborer Profile Endpoints
    getDayLaborerProfile: builder.query<DayLaborerProfileData | null, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const { data, error } = await supabase
            .from('day_laborer_profiles')
            .select('*')
            .eq('user_id', user.id)
            .single()

          if (error && error.code !== 'PGRST116') throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['DayLaborerProfile'],
    }),

    updateDayLaborerProfile: builder.mutation<DayLaborerProfileData, Partial<DayLaborerProfileData>>({
      queryFn: async (profileData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          const updateData = {
            ...profileData,
            user_id: user.id,
            updated_at: new Date().toISOString(),
          }

          const { data, error } = await supabase
            .from('day_laborer_profiles')
            .upsert(updateData, { onConflict: 'user_id' })
            .select()
            .single()

          if (error) throw error
          return { data }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      invalidatesTags: ['DayLaborerProfile'],
    }),

    // Get profile completion status for all roles
    getProfileCompletionStatus: builder.query<Record<string, { completed: boolean; percentage: number }>, void>({
      queryFn: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (!user) throw new Error('Not authenticated')

          // Get user roles
          const { data: userRoles } = await supabase
            .from('user_roles')
            .select('role')
            .eq('user_id', user.id)

          const status: Record<string, { completed: boolean; percentage: number }> = {}

          if (userRoles) {
            for (const roleData of userRoles) {
              const role = roleData.role
              let completed = false
              let percentage = 0

              if (role === 'homeowner') {
                const { data: profile } = await supabase
                  .from('homeowner_profiles')
                  .select('*')
                  .eq('user_id', user.id)
                  .single()

                if (profile) {
                  const fields = ['property_type', 'address', 'preferred_service_categories', 'communication_preferences']
                  const completedFields = fields.filter(field => profile[field])
                  percentage = Math.round((completedFields.length / fields.length) * 100)
                  completed = profile.profile_completed || false
                }
              } else if (role === 'contractor') {
                const { data: profile } = await supabase
                  .from('contractor_profiles')
                  .select('*')
                  .eq('user_id', user.id)
                  .single()

                if (profile) {
                  const fields = ['business_name', 'specializations', 'years_experience', 'service_radius']
                  const completedFields = fields.filter(field => profile[field])
                  percentage = Math.round((completedFields.length / fields.length) * 100)
                  completed = profile.profile_completed || false
                }
              } else if (role === 'day_laborer') {
                const { data: profile } = await supabase
                  .from('day_laborer_profiles')
                  .select('*')
                  .eq('user_id', user.id)
                  .single()

                if (profile) {
                  const fields = ['skill_categories', 'years_experience', 'preferred_job_types', 'hourly_rate']
                  const completedFields = fields.filter(field => profile[field])
                  percentage = Math.round((completedFields.length / fields.length) * 100)
                  completed = profile.profile_completed || false
                }
              }

              status[role] = { completed, percentage }
            }
          }

          return { data: status }
        } catch (error: any) {
          return { error: { status: 'FETCH_ERROR', error: error.message } }
        }
      },
      providesTags: ['HomeownerProfile', 'ContractorProfile', 'DayLaborerProfile'],
    }),
  }),
})

export const {
  useGetHomeownerProfileQuery,
  useUpdateHomeownerProfileMutation,
  useGetContractorProfileQuery,
  useUpdateContractorProfileMutation,
  useGetDayLaborerProfileQuery,
  useUpdateDayLaborerProfileMutation,
  useGetProfileCompletionStatusQuery,
} = profileCompletionApi
