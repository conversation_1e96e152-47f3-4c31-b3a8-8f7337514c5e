'use client'

import { Check<PERSON>ir<PERSON>, Circle } from 'lucide-react'

interface Step {
  id: string
  title: string
  description?: string
}

interface ProgressIndicatorProps {
  steps: Step[]
  currentStep: number
  completedSteps: number[]
  className?: string
}

export function ProgressIndicator({ 
  steps, 
  currentStep, 
  completedSteps, 
  className = '' 
}: ProgressIndicatorProps) {
  const progressPercentage = Math.round((completedSteps.length / steps.length) * 100)

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Profile Completion</span>
          <span className="font-medium text-orange-600 dark:text-orange-400">
            {progressPercentage}% Complete
          </span>
        </div>
        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-orange-600 to-red-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Step Indicators */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const stepNumber = index + 1
          const isCompleted = completedSteps.includes(stepNumber)
          const isCurrent = currentStep === stepNumber
          const isUpcoming = stepNumber > currentStep

          return (
            <div 
              key={step.id}
              className={`flex items-start gap-3 p-3 rounded-lg transition-all duration-200 ${
                isCurrent 
                  ? 'bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800' 
                  : isCompleted
                  ? 'bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800'
                  : 'bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700'
              }`}
            >
              {/* Step Icon */}
              <div className={`flex-shrink-0 mt-0.5 ${
                isCompleted 
                  ? 'text-emerald-600 dark:text-emerald-400'
                  : isCurrent
                  ? 'text-orange-600 dark:text-orange-400'
                  : 'text-slate-400 dark:text-slate-500'
              }`}>
                {isCompleted ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center text-xs font-medium ${
                    isCurrent
                      ? 'border-orange-600 dark:border-orange-400 bg-orange-600 dark:bg-orange-400 text-white'
                      : 'border-slate-300 dark:border-slate-600 text-slate-500 dark:text-slate-400'
                  }`}>
                    {stepNumber}
                  </div>
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <h3 className={`font-medium text-sm ${
                  isCompleted
                    ? 'text-emerald-900 dark:text-emerald-100'
                    : isCurrent
                    ? 'text-orange-900 dark:text-orange-100'
                    : isUpcoming
                    ? 'text-slate-500 dark:text-slate-400'
                    : 'text-slate-700 dark:text-slate-300'
                }`}>
                  {step.title}
                </h3>
                {step.description && (
                  <p className={`text-xs mt-1 ${
                    isCompleted
                      ? 'text-emerald-700 dark:text-emerald-300'
                      : isCurrent
                      ? 'text-orange-700 dark:text-orange-300'
                      : 'text-slate-500 dark:text-slate-400'
                  }`}>
                    {step.description}
                  </p>
                )}
              </div>

              {/* Status Badge */}
              <div className="flex-shrink-0">
                {isCompleted && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200">
                    Complete
                  </span>
                )}
                {isCurrent && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200">
                    Current
                  </span>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
