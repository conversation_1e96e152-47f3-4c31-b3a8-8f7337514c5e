import Link from "next/link";
import { Home } from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-slate-50 dark:bg-slate-900 border-t border-slate-200 dark:border-slate-800 py-16 lg:py-20">
      <div className="container grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
        <div className="lg:col-span-2">
          <Link href="/" className="flex items-center space-x-3 mb-6 group">
            <div className="p-2 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl group-hover:from-orange-700 group-hover:to-red-700 transition-all duration-200">
              <Home className="h-6 w-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              BuildPro
            </span>
          </Link>
          <p className="text-base text-muted-foreground max-w-md leading-relaxed mb-6">
            Connecting homeowners with skilled contractors and day laborers for
            all your home improvement needs. Build your dream home with trusted
            professionals.
          </p>
          <div className="flex space-x-4">
            {[
              { name: "Facebook", icon: "📘" },
              { name: "Twitter", icon: "🐦" },
              { name: "Instagram", icon: "📷" },
              { name: "LinkedIn", icon: "💼" },
            ].map((social) => (
              <a
                key={social.name}
                href="#"
                className="w-10 h-10 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl flex items-center justify-center hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:border-orange-300 dark:hover:border-orange-600 transition-all duration-200 hover:scale-110"
                aria-label={social.name}
              >
                <span className="text-lg">{social.icon}</span>
              </a>
            ))}
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-slate-900 dark:text-white mb-6">
            Categories
          </h3>
          <ul className="space-y-3">
            {[
              { name: "Home Renovation", href: "/categories/home-renovation" },
              { name: "Handyman Services", href: "/categories/handyman" },
              { name: "Landscaping", href: "/categories/landscaping" },
              { name: "Cleaning Services", href: "/categories/cleaning" },
              { name: "Painting", href: "/categories/painting" },
            ].map((category) => (
              <li key={category.name}>
                <Link
                  href={category.href}
                  className="text-sm text-muted-foreground hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
                >
                  {category.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-semibold text-slate-900 dark:text-white mb-6">
            About
          </h3>
          <ul className="space-y-3">
            {[
              { name: "About Us", href: "/about" },
              { name: "How It Works", href: "/how-it-works" },
              { name: "Careers", href: "/careers" },
              { name: "Blog", href: "/blog" },
            ].map((link) => (
              <li key={link.name}>
                <Link
                  href={link.href}
                  className="text-sm text-muted-foreground hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
                >
                  {link.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-semibold text-slate-900 dark:text-white mb-6">
            Support
          </h3>
          <ul className="space-y-3">
            {[
              { name: "Help Center", href: "/help-center" },
              { name: "Privacy Policy", href: "/privacy" },
              { name: "Terms of Service", href: "/terms" },
              { name: "Contact Us", href: "/contact" },
            ].map((link) => (
              <li key={link.name}>
                <Link
                  href={link.href}
                  className="text-sm text-muted-foreground hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
                >
                  {link.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="container mt-12 pt-8 border-t border-slate-200 dark:border-slate-700">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} BuildPro. All rights reserved.
          </p>
          <div className="flex items-center gap-6 mt-4 md:mt-0">
            <Link
              href="/language"
              className="text-sm text-muted-foreground hover:text-orange-600 dark:hover:text-orange-400 transition-colors font-medium"
            >
              🌐 English
            </Link>
            <Link
              href="/currency"
              className="text-sm text-muted-foreground hover:text-orange-600 dark:hover:text-orange-400 transition-colors font-medium"
            >
              💰 USD
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
