'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { supabase } from '@/lib/supabase/client'
import { openResetPasswordModal } from '@/lib/store/slices/uiSlice'
import { addNotification } from '@/lib/store/slices/uiSlice'
import { Loader2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function ResetPasswordPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const dispatch = useDispatch()
  const [isVerifying, setIsVerifying] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const verifyResetToken = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Reset password error:', error)
          setError('Invalid or expired reset link. Please request a new password reset.')
          setIsVerifying(false)
          return
        }

        if (data.session) {
          // User is authenticated with reset token, open reset password modal
          dispatch(openResetPasswordModal())
          dispatch(addNotification({
            type: 'info',
            message: 'Please enter your new password below.',
            duration: 3000,
          }))
          router.push('/')
        } else {
          setError('Invalid or expired reset link. Please request a new password reset.')
        }
      } catch (error) {
        console.error('Unexpected error:', error)
        setError('An unexpected error occurred. Please try again.')
      } finally {
        setIsVerifying(false)
      }
    }

    verifyResetToken()
  }, [router, dispatch])

  if (isVerifying) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-orange-600" />
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
            Verifying reset link...
          </h2>
          <p className="text-muted-foreground">
            Please wait while we verify your password reset request.
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
              Reset Link Invalid
            </h2>
            <p className="text-muted-foreground">
              {error}
            </p>
          </div>

          <div className="space-y-3">
            <Link href="/">
              <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                Go to Homepage
              </Button>
            </Link>
            <p className="text-sm text-muted-foreground">
              You can request a new password reset from the sign-in page.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // This shouldn't be reached, but just in case
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20">
      <div className="text-center space-y-4">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
          Redirecting...
        </h2>
        <p className="text-muted-foreground">
          Please wait while we redirect you.
        </p>
      </div>
    </div>
  )
}
