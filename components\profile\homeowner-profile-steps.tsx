"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useDispatch } from "react-redux";
import {
  useGetHomeownerProfileQuery,
  useUpdateHomeownerProfileMutation,
  HomeownerProfileData,
} from "@/lib/store/api/profileCompletionApi";
import { addNotification } from "@/lib/store/slices/uiSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ProgressIndicator } from "@/components/ui/progress-indicator";
import {
  ArrowLeft,
  ArrowRight,
  Home,
  MapPin,
  DollarSign,
  Bell,
} from "lucide-react";

// Step 1: Basic Information Schema
const basicInfoSchema = z.object({
  property_address: z.string().min(1, "Property address is required"),
  property_type: z.enum([
    "single_family",
    "townhouse",
    "condo",
    "apartment",
    "commercial",
  ]),
  property_size: z.enum(["small", "medium", "large", "extra_large"]),
  preferred_categories: z
    .array(z.string())
    .min(1, "Select at least one service category"),
  budget_range: z.enum([
    "under_1000",
    "1000_5000",
    "5000_15000",
    "15000_50000",
    "over_50000",
  ]),
});

// Step 2: Preferences Schema
const preferencesSchema = z.object({
  communication_preference: z.enum(["email", "phone", "text", "app"]),
  notification_settings: z.object({
    project_updates: z.boolean(),
    new_bids: z.boolean(),
    messages: z.boolean(),
    marketing: z.boolean(),
  }),
  service_area_radius: z.enum(["5", "10", "25", "50", "100"]),
  preferred_contact_times: z.array(z.string()),
});

type BasicInfoData = z.infer<typeof basicInfoSchema>;
type PreferencesData = z.infer<typeof preferencesSchema>;

interface HomeownerProfileStepsProps {
  onComplete: (data: BasicInfoData & PreferencesData) => void;
  onBack?: () => void;
  initialData?: Partial<BasicInfoData & PreferencesData>;
}

export function HomeownerProfileSteps({
  onComplete,
  onBack,
  initialData,
}: HomeownerProfileStepsProps) {
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [formData, setFormData] = useState<
    Partial<BasicInfoData & PreferencesData>
  >(initialData || {});

  // API hooks
  const { data: existingProfile, isLoading: isLoadingProfile } =
    useGetHomeownerProfileQuery();
  const [updateProfile] = useUpdateHomeownerProfileMutation();

  // Load existing profile data
  useEffect(() => {
    if (existingProfile) {
      setFormData({
        property_address: existingProfile.address || "",
        property_type: existingProfile.property_type || "single_family",
        property_size: existingProfile.property_size || "medium",
        preferred_categories:
          existingProfile.preferred_service_categories || [],
        budget_range: existingProfile.budget_range || "5000_15000",
        communication_preference:
          existingProfile.communication_preferences?.[0] || "email",
        service_area_radius:
          existingProfile.service_area_radius?.toString() || "25",
        preferred_contact_times: existingProfile.preferred_contact_times || [],
        notification_settings: {
          project_updates:
            existingProfile.notification_settings?.includes(
              "project_updates"
            ) || true,
          new_bids:
            existingProfile.notification_settings?.includes("new_bids") || true,
          messages:
            existingProfile.notification_settings?.includes("messages") || true,
          marketing:
            existingProfile.notification_settings?.includes("marketing") ||
            false,
        },
      });
    }
  }, [existingProfile]);

  const steps = [
    {
      id: "basic-info",
      title: "Basic Information",
      description: "Property details and service preferences",
    },
    {
      id: "preferences",
      title: "Preferences",
      description: "Communication and notification settings",
    },
  ];

  const serviceCategories = [
    "Plumbing",
    "Electrical",
    "HVAC",
    "Roofing",
    "Flooring",
    "Painting",
    "Landscaping",
    "Cleaning",
    "Handyman",
    "Kitchen Renovation",
    "Bathroom Renovation",
    "General Contracting",
  ];

  const contactTimes = [
    "Early Morning (6-9 AM)",
    "Morning (9-12 PM)",
    "Afternoon (12-5 PM)",
    "Evening (5-8 PM)",
    "Late Evening (8-10 PM)",
  ];

  // Step 1: Basic Information Form
  const BasicInfoStep = () => {
    const form = useForm<BasicInfoData>({
      resolver: zodResolver(basicInfoSchema),
      defaultValues: {
        property_address: formData.property_address || "",
        property_type: formData.property_type || "single_family",
        property_size: formData.property_size || "medium",
        preferred_categories: formData.preferred_categories || [],
        budget_range: formData.budget_range || "5000_15000",
      },
    });

    const onSubmit = (data: BasicInfoData) => {
      setFormData((prev) => ({ ...prev, ...data }));
      setCompletedSteps((prev) => [...prev.filter((s) => s !== 1), 1]);
      setCurrentStep(2);
    };

    return (
      <div className="space-y-8">
        {/* Step Header */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Home className="h-4 w-4" />
            Step 1 of 2
          </div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Property Information
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            Tell us about your property to help contractors provide accurate
            quotes
          </p>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Home className="h-6 w-6" />
                </div>
                Property Details
              </CardTitle>
              <CardDescription className="text-blue-100">
                Help us understand your property better
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="property_address">Property Address</Label>
                <Input
                  id="property_address"
                  placeholder="123 Main St, City, State, ZIP"
                  {...form.register("property_address")}
                />
                {form.formState.errors.property_address && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.property_address.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="property_type">Property Type</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("property_type", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single_family">
                        Single Family Home
                      </SelectItem>
                      <SelectItem value="townhouse">Townhouse</SelectItem>
                      <SelectItem value="condo">Condominium</SelectItem>
                      <SelectItem value="apartment">Apartment</SelectItem>
                      <SelectItem value="commercial">
                        Commercial Property
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_size">Property Size</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue("property_size", value as any)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">
                        Small (Under 1,000 sq ft)
                      </SelectItem>
                      <SelectItem value="medium">
                        Medium (1,000-2,500 sq ft)
                      </SelectItem>
                      <SelectItem value="large">
                        Large (2,500-5,000 sq ft)
                      </SelectItem>
                      <SelectItem value="extra_large">
                        Extra Large (Over 5,000 sq ft)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <DollarSign className="h-6 w-6" />
                </div>
                Service Preferences
              </CardTitle>
              <CardDescription className="text-emerald-100">
                Select the services you're most likely to need
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Preferred Service Categories</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {serviceCategories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={category}
                        checked={form
                          .watch("preferred_categories")
                          ?.includes(category)}
                        onCheckedChange={(checked) => {
                          const current =
                            form.getValues("preferred_categories") || [];
                          if (checked) {
                            form.setValue("preferred_categories", [
                              ...current,
                              category,
                            ]);
                          } else {
                            form.setValue(
                              "preferred_categories",
                              current.filter((c) => c !== category)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={category} className="text-sm">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
                {form.formState.errors.preferred_categories && (
                  <p className="text-sm text-red-600">
                    {form.formState.errors.preferred_categories.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="budget_range">
                  Typical Project Budget Range
                </Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("budget_range", value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="under_1000">Under $1,000</SelectItem>
                    <SelectItem value="1000_5000">$1,000 - $5,000</SelectItem>
                    <SelectItem value="5000_15000">$5,000 - $15,000</SelectItem>
                    <SelectItem value="15000_50000">
                      $15,000 - $50,000
                    </SelectItem>
                    <SelectItem value="over_50000">Over $50,000</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="min-w-[120px] border-slate-300 hover:bg-slate-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button
              type="submit"
              className="min-w-[160px] bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Continue to Preferences
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </form>
      </div>
    );
  };

  // Step 2: Preferences Form
  const PreferencesStep = () => {
    const form = useForm<PreferencesData>({
      resolver: zodResolver(preferencesSchema),
      defaultValues: {
        communication_preference: formData.communication_preference || "email",
        notification_settings: formData.notification_settings || {
          project_updates: true,
          new_bids: true,
          messages: true,
          marketing: false,
        },
        service_area_radius: formData.service_area_radius || "25",
        preferred_contact_times: formData.preferred_contact_times || [],
      },
    });

    const onSubmit = async (data: PreferencesData) => {
      const finalData = { ...formData, ...data } as BasicInfoData &
        PreferencesData;
      setCompletedSteps((prev) => [...prev.filter((s) => s !== 2), 2]);

      try {
        // Convert form data to Supabase format
        const profileData: Partial<HomeownerProfileData> = {
          address: finalData.property_address,
          property_type: finalData.property_type,
          property_size: finalData.property_size,
          preferred_service_categories: finalData.preferred_categories,
          budget_range: finalData.budget_range,
          communication_preferences: [finalData.communication_preference],
          service_area_radius: parseInt(finalData.service_area_radius),
          preferred_contact_times: finalData.preferred_contact_times,
          notification_settings: Object.entries(finalData.notification_settings)
            .filter(([_, enabled]) => enabled)
            .map(([key, _]) => key),
          profile_completed: true,
        };

        await updateProfile(profileData).unwrap();

        dispatch(
          addNotification({
            type: "success",
            message: "Homeowner profile completed successfully!",
            duration: 5000,
          })
        );

        onComplete(finalData);
      } catch (error: any) {
        dispatch(
          addNotification({
            type: "error",
            message: error.error || "Failed to save profile. Please try again.",
            duration: 5000,
          })
        );
      }
    };

    return (
      <div className="space-y-8">
        {/* Step Header */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-800 dark:text-orange-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Bell className="h-4 w-4" />
            Step 2 of 2
          </div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
            Communication & Preferences
          </h2>
          <p className="text-slate-600 dark:text-slate-300">
            Set up how you'd like to be contacted and receive notifications
          </p>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-orange-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Bell className="h-6 w-6" />
                </div>
                Communication Preferences
              </CardTitle>
              <CardDescription className="text-orange-100">
                How would you like contractors and our platform to contact you?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="communication_preference">
                  Preferred Communication Method
                </Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("communication_preference", value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select communication method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="phone">Phone Call</SelectItem>
                    <SelectItem value="text">Text Message</SelectItem>
                    <SelectItem value="app">In-App Messaging</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Preferred Contact Times</Label>
                <div className="space-y-2">
                  {contactTimes.map((time) => (
                    <div key={time} className="flex items-center space-x-2">
                      <Checkbox
                        id={time}
                        checked={form
                          .watch("preferred_contact_times")
                          ?.includes(time)}
                        onCheckedChange={(checked) => {
                          const current =
                            form.getValues("preferred_contact_times") || [];
                          if (checked) {
                            form.setValue("preferred_contact_times", [
                              ...current,
                              time,
                            ]);
                          } else {
                            form.setValue(
                              "preferred_contact_times",
                              current.filter((t) => t !== time)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={time} className="text-sm">
                        {time}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50/30 dark:from-slate-800 dark:to-slate-900">
            <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <MapPin className="h-6 w-6" />
                </div>
                Service Area & Notifications
              </CardTitle>
              <CardDescription className="text-emerald-100">
                Set your service area and notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="service_area_radius">Service Area Radius</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("service_area_radius", value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service radius" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 miles</SelectItem>
                    <SelectItem value="10">10 miles</SelectItem>
                    <SelectItem value="25">25 miles</SelectItem>
                    <SelectItem value="50">50 miles</SelectItem>
                    <SelectItem value="100">100 miles</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Notification Settings</Label>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label
                        htmlFor="project_updates"
                        className="text-sm font-medium"
                      >
                        Project Updates
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Get notified about project progress
                      </p>
                    </div>
                    <Checkbox
                      id="project_updates"
                      checked={form.watch(
                        "notification_settings.project_updates"
                      )}
                      onCheckedChange={(checked) =>
                        form.setValue(
                          "notification_settings.project_updates",
                          checked as boolean
                        )
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="new_bids" className="text-sm font-medium">
                        New Bids
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Get notified when contractors bid on your projects
                      </p>
                    </div>
                    <Checkbox
                      id="new_bids"
                      checked={form.watch("notification_settings.new_bids")}
                      onCheckedChange={(checked) =>
                        form.setValue(
                          "notification_settings.new_bids",
                          checked as boolean
                        )
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="messages" className="text-sm font-medium">
                        Messages
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Get notified about new messages
                      </p>
                    </div>
                    <Checkbox
                      id="messages"
                      checked={form.watch("notification_settings.messages")}
                      onCheckedChange={(checked) =>
                        form.setValue(
                          "notification_settings.messages",
                          checked as boolean
                        )
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label
                        htmlFor="marketing"
                        className="text-sm font-medium"
                      >
                        Marketing & Tips
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Receive helpful tips and platform updates
                      </p>
                    </div>
                    <Checkbox
                      id="marketing"
                      checked={form.watch("notification_settings.marketing")}
                      onCheckedChange={(checked) =>
                        form.setValue(
                          "notification_settings.marketing",
                          checked as boolean
                        )
                      }
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setCurrentStep(1)}
              className="min-w-[120px] border-slate-300 hover:bg-slate-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              type="submit"
              className="min-w-[180px] bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Complete Profile ✨
            </Button>
          </div>
        </form>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {currentStep === 1 && <BasicInfoStep />}
      {currentStep === 2 && <PreferencesStep />}
    </div>
  );
}
