"use client";

import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { RootState } from "@/lib/store";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Briefcase,
  Hammer,
  Home,
  Star,
  TrendingUp,
  Users,
  ArrowRight,
  CheckCircle,
  Clock,
  MapPin,
} from "lucide-react";
import { openRoleSelectionModal } from "@/lib/store/slices/uiSlice";

export function GeneralDashboard() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { user, profile } = useSelector((state: RootState) => state.auth);

  const handleRoleAction = (
    role: "homeowner" | "contractor" | "day_laborer"
  ) => {
    // Open role selection modal which will redirect to profile completion page
    dispatch(openRoleSelectionModal());
  };

  const platformStats = [
    { label: "Active Projects", value: "2,847", icon: Briefcase },
    { label: "Skilled Contractors", value: "12,500+", icon: Hammer },
    { label: "Happy Homeowners", value: "8,200+", icon: Home },
    { label: "Average Rating", value: "4.8/5", icon: Star },
  ];

  const successStories = [
    {
      id: 1,
      type: "homeowner",
      name: "Sarah Johnson",
      story:
        "Found an amazing contractor for my kitchen renovation. The project was completed on time and within budget!",
      project: "Kitchen Renovation",
      rating: 5,
      image:
        "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      id: 2,
      type: "contractor",
      name: "Mike Rodriguez",
      story:
        "BuildPro helped me grow my business by 300%. I now have a steady stream of quality projects.",
      specialty: "Plumbing & HVAC",
      rating: 4.9,
      image:
        "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      id: 3,
      type: "day_laborer",
      name: "Carlos Martinez",
      story:
        "I find consistent work that fits my schedule. The payment system is reliable and fast.",
      skills: "Landscaping & Moving",
      rating: 4.8,
      image:
        "https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
  ];

  const roleCards = [
    {
      role: "homeowner",
      title: "Post a Job",
      description: "Get your home projects done by skilled professionals",
      benefits: [
        "Find vetted contractors",
        "Compare quotes",
        "Secure payments",
      ],
      cta: "Start Your Project",
      color:
        "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800",
      icon: Home,
      iconColor: "text-blue-600 dark:text-blue-400",
    },
    {
      role: "contractor",
      title: "Find Work",
      description: "Grow your business with quality projects and clients",
      benefits: ["Access to projects", "Build reputation", "Steady income"],
      cta: "Start Bidding",
      color:
        "bg-emerald-50 dark:bg-emerald-950/20 border-emerald-200 dark:border-emerald-800",
      icon: Hammer,
      iconColor: "text-emerald-600 dark:text-emerald-400",
    },
    {
      role: "day_laborer",
      title: "Offer Services",
      description: "Find flexible work that fits your schedule",
      benefits: ["Flexible hours", "Quick payments", "Local opportunities"],
      cta: "Find Gigs",
      color:
        "bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800",
      icon: Briefcase,
      iconColor: "text-orange-600 dark:text-orange-400",
    },
  ];

  const recentProjects = [
    {
      id: 1,
      title: "Kitchen Renovation",
      location: "San Francisco, CA",
      budget: "$15,000 - $25,000",
      postedTime: "2 hours ago",
      bids: 8,
      category: "Home Improvement",
    },
    {
      id: 2,
      title: "Bathroom Remodel",
      location: "Los Angeles, CA",
      budget: "$8,000 - $12,000",
      postedTime: "4 hours ago",
      bids: 12,
      category: "Plumbing",
    },
    {
      id: 3,
      title: "Landscaping Project",
      location: "San Diego, CA",
      budget: "$3,000 - $5,000",
      postedTime: "6 hours ago",
      bids: 5,
      category: "Landscaping",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-orange-950/20 dark:via-background dark:to-red-950/20">
      <div className="container py-8 space-y-8">
        {/* Welcome Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-white">
            Welcome to BuildPro, {user?.user_metadata?.full_name || "there"}! 👋
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Discover how thousands of homeowners, contractors, and day laborers
            are transforming the home services industry. Explore what's possible
            on our platform.
          </p>
        </div>

        {/* Platform Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {platformStats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <stat.icon className="h-8 w-8 mx-auto mb-2 text-orange-600 dark:text-orange-400" />
                <div className="text-2xl font-bold text-slate-900 dark:text-white">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Role Action Cards */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
              What would you like to do?
            </h2>
            <p className="text-muted-foreground">
              Choose your path and start your journey with BuildPro
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {roleCards.map((card) => (
              <Card
                key={card.role}
                className={`${card.color} hover:shadow-lg transition-all duration-300 cursor-pointer group`}
              >
                <CardHeader className="text-center">
                  <div
                    className={`w-16 h-16 mx-auto rounded-full bg-white dark:bg-slate-800 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}
                  >
                    <card.icon className={`h-8 w-8 ${card.iconColor}`} />
                  </div>
                  <CardTitle className="text-xl">{card.title}</CardTitle>
                  <CardDescription className="text-base">
                    {card.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2">
                    {card.benefits.map((benefit, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-sm"
                      >
                        <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <Button
                    onClick={() => handleRoleAction(card.role as any)}
                    className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    {card.cta}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Success Stories */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
              Success Stories
            </h2>
            <p className="text-muted-foreground">
              See how BuildPro is helping people achieve their goals
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {successStories.map((story) => (
              <Card
                key={story.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardContent className="pt-6">
                  <div className="flex items-start gap-4 mb-4">
                    <img
                      src={story.image}
                      alt={story.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-slate-900 dark:text-white">
                        {story.name}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {story.type === "homeowner"
                            ? "Homeowner"
                            : story.type === "contractor"
                            ? "Contractor"
                            : "Day Laborer"}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-muted-foreground">
                            {story.rating}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    "{story.story}"
                  </p>
                  {story.project && (
                    <div className="text-xs text-muted-foreground">
                      Project: {story.project}
                    </div>
                  )}
                  {story.specialty && (
                    <div className="text-xs text-muted-foreground">
                      Specialty: {story.specialty}
                    </div>
                  )}
                  {story.skills && (
                    <div className="text-xs text-muted-foreground">
                      Skills: {story.skills}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Projects Preview */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                Recent Projects
              </h2>
              <p className="text-muted-foreground">
                See what homeowners are looking for
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => handleRoleAction("contractor")}
            >
              View All Projects
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {recentProjects.map((project) => (
              <Card
                key={project.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
              >
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="font-semibold text-slate-900 dark:text-white line-clamp-2">
                        {project.title}
                      </h3>
                      <Badge variant="secondary" className="text-xs">
                        {project.category}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {project.location}
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        {project.budget}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {project.postedTime}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-2 border-t">
                      <span className="text-sm text-muted-foreground">
                        {project.bids} bids
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRoleAction("contractor")}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-orange-600 to-red-600 text-white border-0">
          <CardContent className="pt-8 pb-8 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl mb-6 text-orange-100">
              Join thousands of users who are already transforming their home
              service experience
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                onClick={() => handleRoleAction("homeowner")}
                className="bg-white text-orange-600 hover:bg-orange-50"
              >
                Post Your First Project
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => handleRoleAction("contractor")}
                className="border-white text-white hover:bg-white/10"
              >
                Start Finding Work
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
