'use client'

import { useState } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/store'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Bell, 
  Home, 
  Briefcase, 
  Hammer, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Star,
  MessageSquare,
  DollarSign,
  Calendar,
  User
} from 'lucide-react'
import { UserRole } from '@/lib/supabase/types'

interface Notification {
  id: string
  type: 'profile_completion' | 'new_project' | 'bid_received' | 'message' | 'payment' | 'review' | 'system'
  role?: UserRole
  title: string
  description: string
  timestamp: string
  read: boolean
  priority: 'low' | 'medium' | 'high'
  actionUrl?: string
}

export function NotificationCenter() {
  const { selectedRoles, profileCompletionStatus } = useSelector((state: RootState) => state.auth)
  const [notifications, setNotifications] = useState<Notification[]>([
    // Mock notifications - in real app, these would come from API
    {
      id: '1',
      type: 'profile_completion',
      role: 'homeowner',
      title: 'Complete Your Homeowner Profile',
      description: 'Add your property details to get better project matches',
      timestamp: '2 hours ago',
      read: false,
      priority: 'medium',
      actionUrl: '/profile/complete'
    },
    {
      id: '2',
      type: 'new_project',
      role: 'contractor',
      title: 'New Project Available',
      description: 'Kitchen renovation in San Francisco - $15,000 budget',
      timestamp: '4 hours ago',
      read: false,
      priority: 'high'
    },
    {
      id: '3',
      type: 'bid_received',
      role: 'homeowner',
      title: 'New Bid Received',
      description: 'Mike Rodriguez submitted a bid for your bathroom project',
      timestamp: '1 day ago',
      read: true,
      priority: 'medium'
    },
    {
      id: '4',
      type: 'message',
      title: 'New Message',
      description: 'Sarah Johnson sent you a message about the plumbing project',
      timestamp: '2 days ago',
      read: true,
      priority: 'low'
    }
  ])

  const unreadCount = notifications.filter(n => !n.read).length

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'profile_completion':
        return <User className="h-4 w-4" />
      case 'new_project':
        return <Briefcase className="h-4 w-4" />
      case 'bid_received':
        return <DollarSign className="h-4 w-4" />
      case 'message':
        return <MessageSquare className="h-4 w-4" />
      case 'payment':
        return <DollarSign className="h-4 w-4" />
      case 'review':
        return <Star className="h-4 w-4" />
      case 'system':
        return <Info className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getRoleIcon = (role?: UserRole) => {
    if (!role) return null
    switch (role) {
      case 'homeowner':
        return <Home className="h-3 w-3 text-blue-600" />
      case 'contractor':
        return <Briefcase className="h-3 w-3 text-emerald-600" />
      case 'day_laborer':
        return <Hammer className="h-3 w-3 text-orange-600" />
      default:
        return null
    }
  }

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50 dark:bg-red-950/20'
      case 'medium':
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-950/20'
      case 'low':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-950/20'
      default:
        return 'border-l-slate-300 bg-slate-50 dark:bg-slate-800'
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  }

  // Generate profile completion notifications
  const profileNotifications: Notification[] = []
  if (selectedRoles && profileCompletionStatus) {
    selectedRoles.forEach(role => {
      const status = profileCompletionStatus[role]
      if (status && !status.completed && status.percentage < 100) {
        profileNotifications.push({
          id: `profile-${role}`,
          type: 'profile_completion',
          role,
          title: `Complete Your ${role.replace('_', ' ')} Profile`,
          description: `${status.percentage}% complete - Unlock more features by finishing your profile`,
          timestamp: 'Pending',
          read: false,
          priority: 'medium',
          actionUrl: '/profile/complete'
        })
      }
    })
  }

  const allNotifications = [...profileNotifications, ...notifications]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 p-0">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Notifications</CardTitle>
              {unreadCount > 0 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={markAllAsRead}
                  className="text-xs"
                >
                  Mark all read
                </Button>
              )}
            </div>
            {unreadCount > 0 && (
              <CardDescription>
                You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-96">
              {allNotifications.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No notifications yet</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {allNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-l-4 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors ${
                        getPriorityColor(notification.priority)
                      } ${!notification.read ? 'font-medium' : 'opacity-75'}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="text-sm font-medium truncate">
                              {notification.title}
                            </p>
                            {notification.role && (
                              <div className="flex-shrink-0">
                                {getRoleIcon(notification.role)}
                              </div>
                            )}
                            {!notification.read && (
                              <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0" />
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">
                            {notification.description}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {notification.timestamp}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
