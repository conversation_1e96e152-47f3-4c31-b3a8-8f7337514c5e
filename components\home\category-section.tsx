import Link from "next/link";
import {
  ChevronLeft,
  ChevronRight,
  <PERSON>,
  <PERSON>,
  Trees as Tree,
  Sparkles,
  Paintbrush,
  Zap,
} from "lucide-react";
import { categories } from "@/lib/data";
import { Button } from "@/components/ui/button";

export function CategorySection() {
  const icons = {
    home: <Home className="h-6 w-6" />,
    hammer: <Hammer className="h-6 w-6" />,
    tree: <Tree className="h-6 w-6" />,
    sparkles: <Sparkles className="h-6 w-6" />,
    paintbrush: <Paintbrush className="h-6 w-6" />,
    zap: <Zap className="h-6 w-6" />,
  };

  return (
    <section className="section-padding bg-slate-50 dark:bg-slate-900/50">
      <div className="container">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12 lg:mb-16">
          <div className="max-w-2xl">
            <h2 className="heading-2 mb-4">Service Categories</h2>
            <p className="body-medium text-muted-foreground">
              Explore the most popular service categories and find the perfect
              professional for your needs
            </p>
          </div>
          <div className="flex space-x-2 mt-6 md:mt-0">
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
              aria-label="Previous category"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 rounded-full hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
              aria-label="Next category"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/categories/${category.id}`}
              className="group flex flex-col items-center p-6 lg:p-8 border rounded-2xl bg-white dark:bg-slate-800 card-hover hover:border-orange-300 dark:hover:border-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20"
            >
              <div className="p-4 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 rounded-2xl mb-4 lg:mb-6 group-hover:from-orange-200 group-hover:to-red-200 dark:group-hover:from-orange-800/40 dark:group-hover:to-red-800/40 transition-all duration-300 group-hover:scale-110">
                <div className="text-orange-600 dark:text-orange-400">
                  {icons[category.icon as keyof typeof icons]}
                </div>
              </div>
              <h3 className="font-semibold text-sm lg:text-base mb-2 text-center group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                {category.name}
              </h3>
              <p className="text-xs lg:text-sm text-muted-foreground text-center">
                {category.services} services
              </p>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
