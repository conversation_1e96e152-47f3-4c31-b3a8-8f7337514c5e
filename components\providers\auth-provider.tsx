"use client";

import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { supabase } from "@/lib/supabase/client";
import { setUser, setLoading } from "@/lib/store/slices/authSlice";
import {
  useGetProfileQuery,
  useGetUserRolesQuery,
} from "@/lib/store/api/profileApi";
import { setProfile, setUserRoles } from "@/lib/store/slices/authSlice";
import { RootState } from "@/lib/store";

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);

  // Get profile and user roles when user is authenticated
  const { data: profile, refetch: refetchProfile } = useGetProfileQuery(
    undefined,
    {
      skip: !user, // Only fetch when user is authenticated
    }
  );

  const { data: userRoles, refetch: refetchUserRoles } = useGetUserRolesQuery(
    undefined,
    {
      skip: !user, // Only fetch when user is authenticated
    }
  );

  useEffect(() => {
    // Set initial loading state
    dispatch(setLoading(true));

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      dispatch(setUser(session?.user ?? null));
      dispatch(setLoading(false));
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log(
        "Auth state change:",
        event,
        session?.user?.email_confirmed_at
      );

      dispatch(setUser(session?.user ?? null));
      dispatch(setLoading(false));

      // Handle specific auth events
      if (event === "SIGNED_OUT") {
        dispatch(setProfile(null));
        dispatch(setUserRoles([]));
      } else if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        // Refetch profile data when user signs in or token refreshes
        // This ensures we get the latest email verification status
        if (session?.user) {
          setTimeout(() => {
            refetchProfile();
            refetchUserRoles();
          }, 100); // Small delay to ensure the session is fully established
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [dispatch, refetchProfile, refetchUserRoles]);

  // Update profile in Redux when data changes
  useEffect(() => {
    if (profile) {
      console.log("Profile updated:", profile.email_verified);
      dispatch(setProfile(profile));
    }
  }, [profile, dispatch]);

  // Update user roles in Redux when data changes
  useEffect(() => {
    if (userRoles) {
      const roles = userRoles.map((role) => role.role);
      dispatch(setUserRoles(roles));
    }
  }, [userRoles, dispatch]);

  return <>{children}</>;
}
