import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase/client';

export interface Quote {
  id: string;
  project_id: string;
  contractor_id: string;
  homeowner_id: string;
  title: string;
  description?: string;
  total_amount: number;
  labor_cost?: number;
  material_cost?: number;
  estimated_duration?: number;
  start_date?: string;
  end_date?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn' | 'expired';
  notes?: string;
  terms_and_conditions?: string;
  valid_until?: string;
  created_at: string;
  updated_at: string;
}

export interface QuoteItem {
  id: string;
  quote_id: string;
  item_type: 'labor' | 'material' | 'equipment' | 'permit' | 'other';
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
}

export interface QuoteStatusHistory {
  id: string;
  quote_id: string;
  old_status?: string;
  new_status: string;
  changed_by: string;
  reason?: string;
  created_at: string;
}

export interface CreateQuoteRequest {
  project_id: string;
  homeowner_id: string;
  title: string;
  description?: string;
  total_amount: number;
  labor_cost?: number;
  material_cost?: number;
  estimated_duration?: number;
  start_date?: string;
  end_date?: string;
  notes?: string;
  terms_and_conditions?: string;
  valid_until?: string;
  items?: Omit<QuoteItem, 'id' | 'quote_id' | 'created_at'>[];
}

export interface UpdateQuoteStatusRequest {
  quote_id: string;
  status: 'accepted' | 'rejected' | 'withdrawn';
  reason?: string;
}

export const quotesApi = createApi({
  reducerPath: 'quotesApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Quote', 'QuoteItem', 'QuoteHistory'],
  endpoints: (builder) => ({
    // Get quotes for a user (contractor or homeowner)
    getQuotes: builder.query<Quote[], { role: 'contractor' | 'homeowner' }>({
      queryFn: async ({ role }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const column = role === 'contractor' ? 'contractor_id' : 'homeowner_id';
          const { data, error } = await supabase
            .from('quotes')
            .select('*')
            .eq(column, user.id)
            .order('created_at', { ascending: false });

          if (error) throw error;
          return { data: data || [] };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: ['Quote'],
    }),

    // Get a specific quote with items
    getQuote: builder.query<Quote & { items: QuoteItem[] }, string>({
      queryFn: async (quoteId) => {
        try {
          const { data: quote, error: quoteError } = await supabase
            .from('quotes')
            .select('*')
            .eq('id', quoteId)
            .single();

          if (quoteError) throw quoteError;

          const { data: items, error: itemsError } = await supabase
            .from('quote_items')
            .select('*')
            .eq('quote_id', quoteId)
            .order('created_at', { ascending: true });

          if (itemsError) throw itemsError;

          return { data: { ...quote, items: items || [] } };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: (result, error, id) => [{ type: 'Quote', id }],
    }),

    // Create a new quote
    createQuote: builder.mutation<Quote, CreateQuoteRequest>({
      queryFn: async (quoteData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { items, ...quoteInfo } = quoteData;

          // Create the quote
          const { data: quote, error: quoteError } = await supabase
            .from('quotes')
            .insert({
              ...quoteInfo,
              contractor_id: user.id,
            })
            .select()
            .single();

          if (quoteError) throw quoteError;

          // Create quote items if provided
          if (items && items.length > 0) {
            const { error: itemsError } = await supabase
              .from('quote_items')
              .insert(
                items.map(item => ({
                  ...item,
                  quote_id: quote.id,
                }))
              );

            if (itemsError) throw itemsError;
          }

          // Create status history entry
          await supabase
            .from('quote_status_history')
            .insert({
              quote_id: quote.id,
              new_status: 'pending',
              changed_by: user.id,
            });

          return { data: quote };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: ['Quote'],
    }),

    // Update quote status (accept/reject/withdraw)
    updateQuoteStatus: builder.mutation<Quote, UpdateQuoteStatusRequest>({
      queryFn: async ({ quote_id, status, reason }) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          // Get current quote to check old status
          const { data: currentQuote, error: fetchError } = await supabase
            .from('quotes')
            .select('status')
            .eq('id', quote_id)
            .single();

          if (fetchError) throw fetchError;

          // Update quote status
          const { data: quote, error: updateError } = await supabase
            .from('quotes')
            .update({ status })
            .eq('id', quote_id)
            .select()
            .single();

          if (updateError) throw updateError;

          // Create status history entry
          await supabase
            .from('quote_status_history')
            .insert({
              quote_id,
              old_status: currentQuote.status,
              new_status: status,
              changed_by: user.id,
              reason,
            });

          return { data: quote };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: (result, error, { quote_id }) => [
        { type: 'Quote', id: quote_id },
        'Quote',
      ],
    }),

    // Get quote status history
    getQuoteHistory: builder.query<QuoteStatusHistory[], string>({
      queryFn: async (quoteId) => {
        try {
          const { data, error } = await supabase
            .from('quote_status_history')
            .select('*')
            .eq('quote_id', quoteId)
            .order('created_at', { ascending: false });

          if (error) throw error;
          return { data: data || [] };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      providesTags: (result, error, id) => [{ type: 'QuoteHistory', id }],
    }),

    // Update quote (for contractors)
    updateQuote: builder.mutation<Quote, Partial<Quote> & { id: string }>({
      queryFn: async (quoteData) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { id, ...updateData } = quoteData;

          const { data: quote, error } = await supabase
            .from('quotes')
            .update(updateData)
            .eq('id', id)
            .eq('contractor_id', user.id) // Ensure only contractor can update
            .select()
            .single();

          if (error) throw error;
          return { data: quote };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'Quote', id },
        'Quote',
      ],
    }),

    // Delete quote (for contractors)
    deleteQuote: builder.mutation<void, string>({
      queryFn: async (quoteId) => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('Not authenticated');

          const { error } = await supabase
            .from('quotes')
            .delete()
            .eq('id', quoteId)
            .eq('contractor_id', user.id); // Ensure only contractor can delete

          if (error) throw error;
          return { data: undefined };
        } catch (error: any) {
          return { error: error.message };
        }
      },
      invalidatesTags: ['Quote'],
    }),
  }),
});

export const {
  useGetQuotesQuery,
  useGetQuoteQuery,
  useCreateQuoteMutation,
  useUpdateQuoteStatusMutation,
  useGetQuoteHistoryQuery,
  useUpdateQuoteMutation,
  useDeleteQuoteMutation,
} = quotesApi;
