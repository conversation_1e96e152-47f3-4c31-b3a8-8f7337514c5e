import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { authApi } from "./api/authApi";
import { profileApi } from "./api/profileApi";
import { profileCompletionApi } from "./api/profileCompletionApi";
import { jobsApi } from "./api/jobsApi";
import { quotesApi } from "./api/quotesApi";
import authReducer from "./slices/authSlice";
import uiReducer from "./slices/uiSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    ui: uiReducer,
    [authApi.reducerPath]: authApi.reducer,
    [profileApi.reducerPath]: profileApi.reducer,
    [profileCompletionApi.reducerPath]: profileCompletionApi.reducer,
    [jobsApi.reducerPath]: jobsApi.reducer,
    [quotesApi.reducerPath]: quotesApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    })
      .concat(authApi.middleware)
      .concat(profileApi.middleware)
      .concat(profileCompletionApi.middleware)
      .concat(jobsApi.middleware)
      .concat(quotesApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
