import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface UIState {
  isSignUpModalOpen: boolean;
  isSignInModalOpen: boolean;
  isRoleSelectionModalOpen: boolean;
  isForgotPasswordModalOpen: boolean;
  isResetPasswordModalOpen: boolean;
  currentModal: string | null;
  notifications: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    message: string;
    duration?: number;
  }>;
}

const initialState: UIState = {
  isSignUpModalOpen: false,
  isSignInModalOpen: false,
  isRoleSelectionModalOpen: false,
  isForgotPasswordModalOpen: false,
  isResetPasswordModalOpen: false,
  currentModal: null,
  notifications: [],
};

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    openSignUpModal: (state) => {
      state.isSignUpModalOpen = true;
      state.isSignInModalOpen = false;
      state.currentModal = "signup";
    },
    closeSignUpModal: (state) => {
      state.isSignUpModalOpen = false;
      state.currentModal = null;
    },
    openSignInModal: (state) => {
      state.isSignInModalOpen = true;
      state.isSignUpModalOpen = false;
      state.currentModal = "signin";
    },
    closeSignInModal: (state) => {
      state.isSignInModalOpen = false;
      state.currentModal = null;
    },
    openRoleSelectionModal: (state) => {
      state.isRoleSelectionModalOpen = true;
      state.currentModal = "role-selection";
    },
    closeRoleSelectionModal: (state) => {
      state.isRoleSelectionModalOpen = false;
      state.currentModal = null;
    },
    openForgotPasswordModal: (state) => {
      state.isForgotPasswordModalOpen = true;
      state.isSignInModalOpen = false;
      state.isSignUpModalOpen = false;
      state.currentModal = "forgot-password";
    },
    closeForgotPasswordModal: (state) => {
      state.isForgotPasswordModalOpen = false;
      state.currentModal = null;
    },
    openResetPasswordModal: (state) => {
      state.isResetPasswordModalOpen = true;
      state.currentModal = "reset-password";
    },
    closeResetPasswordModal: (state) => {
      state.isResetPasswordModalOpen = false;
      state.currentModal = null;
    },
    closeAllModals: (state) => {
      state.isSignUpModalOpen = false;
      state.isSignInModalOpen = false;
      state.isRoleSelectionModalOpen = false;
      state.isForgotPasswordModalOpen = false;
      state.isResetPasswordModalOpen = false;
      state.currentModal = null;
    },
    addNotification: (
      state,
      action: PayloadAction<Omit<UIState["notifications"][0], "id">>
    ) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
  },
});

export const {
  openSignUpModal,
  closeSignUpModal,
  openSignInModal,
  closeSignInModal,
  openRoleSelectionModal,
  closeRoleSelectionModal,
  openForgotPasswordModal,
  closeForgotPasswordModal,
  openResetPasswordModal,
  closeResetPasswordModal,
  closeAllModals,
  addNotification,
  removeNotification,
  clearNotifications,
} = uiSlice.actions;

export default uiSlice.reducer;
